import { Locator, Page, expect } from "@playwright/test";
import { CommonCase } from "./common-case";

export default class GroupsPage {
    private page: Page;
    private cc: CommonCase;

    // Navigation elements
    public dashboardPageLink: Locator;
    public groupsPageLink: Locator;
    public groupsPageLabel: Locator;

    // Header elements
    public logoutButton: Locator;
    public hypercubeButton: Locator;
    public darkLightModeButton: Locator;
    public notificationButton: Locator;
    public userProfileButton: Locator;

    // Main page elements
    public btnAddGroup: Locator;
    public searchInput: Locator;
    public groupsGrid: Locator;
    public gridHeaders: Locator;
    public noDataMessage: Locator;
    public paginationControls: Locator;
    public paginationNext: Locator;
    public paginationPrevious: Locator;
    public groupRows: Locator;

    // Add Group Form Elements
    public groupNameInput: Locator;
    public groupDescriptionInput: Locator;
    public groupLevelsDropdown: Locator;
    public saveButton: Locator;
    public cancelButton: Locator;

    // Group actions
    public editGroupButton: Locator;
    public copyGroupButton: Locator;
    public enableGroupButton: Locator;
    public disableGroupButton: Locator;

    // Delete Group
    public deleteGroupButton: Locator;
    public confirmDeleteButton: Locator;
    public cancelButtonDelete: Locator;

    public successMessage: Locator;
    public errorMessage: Locator;


    public groupListLabel: Locator;
    public columnHeaderName: Locator;
    public columnHeaderDescription: Locator;
    public columnHeaderStatus: Locator;
    public columnHeaderUsers: Locator;
    public columnHeaderCreatedBy: Locator;
    public columnHeaderDateCreated: Locator;
    public columnHeaderModifiedBy: Locator;
    public columnHeaderDateModified: Locator;
    public columnHeaderAction: Locator;
    public paginationLabel: Locator;
    public pageSizeDropdown: Locator;
    public firstPageButton: Locator;
    public previousPageButton: Locator;
    public nextPageButton: Locator;
    public lastPageButton: Locator;
    public addGroupLabel: Locator;
    public addGroupFormSaveButton: Locator;
    public groupNameInputFieldlabel: Locator;
    public groupDescriptionInputFieldlabel: Locator;
    public groupAccessLabel: Locator;
    public groupAccessDropdown: Locator;
    public selectLevelsLabel: Locator;
    public storeOption: Locator;
    public timeMeasurePermissionsLabel: Locator;
    public measurePermissionsLabel: Locator;

    public gridcellRowGroupName: Locator;
    public gridcellRoweditButton: Locator;
    public gridcellRowcopyButton: Locator;
    public gridcellRowdisableButton: Locator;
    public gridcellRowdeleteButton: Locator;


    public nameColumnFilterIcon: Locator;
    public nameColumnFilterInput: Locator;
    public nameColumnFilterApplyButton: Locator;
    public nameColumnFilterResetButton: Locator;

    constructor(page: Page) {
        this.page = page;
        this.cc = new CommonCase(this.page);
        this.initializeElements();
    }

    async initializeElements() {
        this.dashboardPageLink = this.page.locator("//mat-icon[normalize-space()='dashboard']");
        this.groupsPageLink = this.page.locator("//mat-icon[normalize-space()='people']");
        this.groupsPageLabel = this.page.getByTestId('appTitle');

        this.btnAddGroup = this.page.getByRole('button', { name: 'Add Group' });
        this.searchInput = this.page.getByPlaceholder('Search');
        this.groupsGrid = this.page.locator('.mat-table');
        this.gridHeaders = this.page.locator('th.mat-header-cell');
        this.noDataMessage = this.page.locator('text=No matching records found');
        this.paginationControls = this.page.locator('.mat-paginator');
        this.paginationNext = this.page.locator('button[aria-label="Next page"]');
        this.paginationPrevious = this.page.locator('button[aria-label="Previous page"]');
        this.groupRows = this.page.locator('tr.mat-row');

        this.groupNameInput = this.page.getByRole('textbox', { name: 'Enter Group Name' });
        this.groupDescriptionInput = this.page.getByRole('textbox', { name: 'Enter Group Description' });
        this.groupLevelsDropdown = this.page.getByRole('combobox', { name: 'Select Levels' });
        this.saveButton = this.page.getByRole('button', { name: 'Save' });
        this.cancelButton = this.page.getByRole('button', { name: 'Cancel' });

        this.editGroupButton = this.page.getByText('edit', { exact: true }).first();
         this.copyGroupButton = this.page.locator("mat-icon", { hasText: "copy" }).first();
        //this.copyGroupButton = this.page.getByText('copy', { exact: true }).first();
        this.disableGroupButton = this.page.getByText('disable', { exact: true }).first();

        // Delete Group   
        this.deleteGroupButton = this.page.locator("//mat-icon[normalize-space()='delete']").first();
        this.confirmDeleteButton = this.page.getByRole('button', { name: 'Delete' });
        this.cancelButtonDelete = this.page.getByRole('button', { name: 'Cancel' });

        this.successMessage = this.page.locator("//div[@class='mat-mdc-snack-bar-label mdc-snackbar__label']");
        this.errorMessage = this.page.locator('.error-message');


        this.groupListLabel = this.page.getByText('Groups List');
        this.columnHeaderName = this.page.getByRole('columnheader', { name: 'Name' });
        this.columnHeaderDescription = this.page.getByRole('columnheader', { name: 'Description' });
        this.columnHeaderStatus = this.page.getByRole('columnheader', { name: 'Status' });
        this.columnHeaderUsers = this.page.getByRole('columnheader', { name: 'Users' });
        this.columnHeaderCreatedBy = this.page.getByRole('columnheader', { name: 'Created By' });
        this.columnHeaderDateCreated = this.page.getByRole('columnheader', { name: 'Date created' });
        this.columnHeaderModifiedBy = this.page.getByRole('columnheader', { name: 'Modified By' });
        this.columnHeaderDateModified = this.page.getByRole('columnheader', { name: 'Date modified' });
        this.columnHeaderAction = this.page.getByRole('columnheader', { name: 'Action' });
        this.paginationLabel = this.page.getByText('Page Size: 10 1 to 1 of 1');
        this.pageSizeDropdown = this.page.getByRole('combobox', { name: 'Page Size' });
        this.firstPageButton = this.page.getByRole('button', { name: 'First Page' });
        this.previousPageButton = this.page.getByRole('button', { name: 'Previous Page' });
        this.nextPageButton = this.page.getByRole('button', { name: 'Next Page' });
        this.lastPageButton = this.page.getByRole('button', { name: 'Last Page' });

        this.addGroupLabel = this.page.getByText('Add Group');
        this.addGroupFormSaveButton = this.page.getByRole('button', { name: 'Save' }).first();
        this.groupNameInputFieldlabel = this.page.getByText('Group Name');
        this.groupDescriptionInputFieldlabel = this.page.getByText('Group Description');
        this.groupAccessLabel = this.page.getByText('Group Access');
        this.selectLevelsLabel = this.page.getByText('Select Levels');
        this.groupNameInput = this.page.getByRole('textbox', { name: 'Enter Group Name' });
        this.groupDescriptionInput = this.page.getByRole('textbox', { name: 'Enter Group Description' });
        this.groupAccessDropdown = this.page.getByRole('combobox', { name: 'Select Levels' });
        this.storeOption = this.page.getByRole('option', { name: 'Store' });
        this.timeMeasurePermissionsLabel = this.page.getByText('Time Measure Permissions');
        this.measurePermissionsLabel = this.page.getByText('Measure Permissions');

        this.gridcellRowGroupName = this.page.getByRole('gridcell', { name: 'Test Group Name' });
        this.gridcellRoweditButton = this.page.getByText('edit').first();
        this.gridcellRowcopyButton = this.page.getByText('copy').first();
        this.gridcellRowdisableButton = this.page.getByText('check_circle').first();
        this.gridcellRowdeleteButton = this.page.getByText('delete').first();

        // Helper Methods
        this.successMessage = this.page.locator("//div[@class='mat-mdc-snack-bar-label mdc-snackbar__label']");
        this.dashboardPageLink = this.page.locator("//mat-icon[normalize-space()='dashboard']");
        this.groupsPageLink = this.page.locator("//mat-icon[normalize-space()='people']");
        this.groupsPageLabel = this.page.getByTestId('appTitle');



        this.nameColumnFilterIcon = this.page.locator('.ag-header-icon > .ag-icon').first();
        this.nameColumnFilterInput = this.page.getByRole('textbox', { name: 'Filter Value' });
        //this.nameColumnFilterInput = this.page.locator('input[placeholder="Filter Value"]').first();
        this.nameColumnFilterApplyButton = this.page.getByRole('button', { name: 'Apply' });
        this.nameColumnFilterResetButton = this.page.getByRole('button', { name: 'Reset' });



    }



    // Navigation methods
    async navigateTo(link: Locator, description: string) {
        console.log(`Dashboard Page - Clicking ${description}`);
        await link.click();
        await this.page.waitForLoadState('networkidle');
    }
    async clickDashboardPageLink() {
        await this.navigateTo(this.dashboardPageLink, "dashboard page link");
    }
    async clickGroupsPageLink() {
        await this.navigateTo(this.groupsPageLink, "groups page link");
    }
    async getPageTitle(): Promise<string> {
        const title = await this.page.title();
        console.log('Actual Page Title - ' + title);
        return title;
    }
    async verifyPageTitle() {
        await this.cc.verifyPageTitle('Groups');
    }

    // Group creation methods
    async clickButtonAddGroup() {
        console.log('Create Groups Page - Clicking Button Add Group');
        await this.btnAddGroup.click();
        await expect(this.groupNameInput).toBeVisible();
    }
    async fillGroupName(groupName: string) {
        console.log('Create Groups Page - Filling Group Name');
        await this.groupNameInput.fill(groupName);
    }
    async fillGroupDescription(groupDescription: string) {
        console.log('Create Groups Page - Filling Group Description');
        await this.groupDescriptionInput.fill(groupDescription);
    }

    async selectGroupLevels(levels: string[]) {
        console.log(`Create Groups Page - Selecting group levels: ${levels.join(', ')}`);

        for (const level of levels) {
            await this.groupLevelsDropdown.click();
            await this.page.getByRole('option', { name: level }).click();
        }
    }
    async clickButtonSaveAddGroupForm() {
        console.log('Create Groups Page - Clicking Button Save to Add New Group');
        await this.addGroupFormSaveButton.click();
    }

    async createGroup(groupName: string, groupDescription: string): Promise<string> {
        console.log('Creating new group');
        console.log(`Filling group form with name: ${groupName}, description: ${groupDescription}`);
        try {
            let message = '';
            await this.fillGroupName(groupName);
            await this.fillGroupDescription(groupDescription);
            await this.clickButtonSaveAddGroupForm();
            if (groupName === '') {
                message = await this.errorMessage.textContent() || '';
                console.log('Error Message: ' + message);
            } else {
                message = await this.waitForSuccess();
                console.log('Success Message: ' + message);
            }
            return message;
        } catch (error) {
            console.error('Error creating group:', error);
            throw error;
        }
    }

    // Group search methods
    async searchForGroup(searchTerm: string) {
        console.log(`Searching for group: ${searchTerm}`);
        try {
            await this.page.waitForTimeout(1000);
            // Make sure the filter icon is visible before clicking
            await this.nameColumnFilterIcon.waitFor({ state: 'visible', timeout: 10000 });
            await this.nameColumnFilterIcon.click();

            // Wait for filter input to be visible
            await this.nameColumnFilterInput.waitFor({ state: 'visible', timeout: 10000 });
            await this.nameColumnFilterInput.fill(searchTerm);

            // Wait for apply button and click it
            await this.nameColumnFilterApplyButton.waitFor({ state: 'visible', timeout: 10000 });
            await this.nameColumnFilterApplyButton.click();

        } catch (error) {
            console.error(`Error searching for group "${searchTerm}":`, error);
            throw error;
        }
    }

    async clearSearch() {
        console.log('Clearing search');
        await this.nameColumnFilterResetButton.waitFor({ state: 'visible' });

        await this.nameColumnFilterIcon.click();
        await this.nameColumnFilterResetButton.click();
    }

    // Group deletion methods
    async deleteGroupButtonClick() {
        console.log('Clicking Delete Group button');
        try {
            await this.deleteGroupButton.waitFor({ state: 'visible', timeout: 5000 });
            await this.deleteGroupButton.click({ force: true });
            await this.page.locator("pag-delete-group");
            await expect(this.page.locator("mat-label", { hasText: "Delete group", })).toBeVisible();
        } catch (error) {
            console.error('Error waiting for page to load:', error);
            throw error;
        }
    }

    async confirmDeleteGroupButtonClick() {
        console.log('Delete Group PopOver - Confirming group deletion');
        await this.confirmDeleteButton.waitFor({ state: 'visible', timeout: 5000 });
        await this.confirmDeleteButton.click();
    }

    async cancelDeleteGroupButtonClick() {
        console.log('Delete Group PopOver - Cancelling group deletion');
        await this.cancelButtonDelete.click();
    }


    async deleteGroup(groupName: string) {
        console.log(`Deleting group: ${groupName}`);
        try {
            await this.searchForGroup(groupName);
            await this.page.waitForTimeout(1000);
            await this.deleteGroupButtonClick();
            await this.confirmDeleteGroupButtonClick();
            await this.page.waitForTimeout(1000);
            const Message = await this.waitForSuccess();
            return Message;
        } catch (error) {
            console.error(`Error deleting group ${groupName}:`, error);
            throw error;
        }
    }


    // Group editing methods
    async editGroup(groupName: string, newDescription: string) {
        console.log(`Editing group: ${groupName}`);
        try {
            await this.searchForGroup(groupName);
            await this.page.locator(`text="${groupName}"`).first().click();
            await this.editGroupButton.click();
            await this.groupDescriptionInput.fill(newDescription);
            await this.clickButtonSaveAddGroupForm();
            const Message = await this.waitForSuccess();
            return Message;
        } catch (error) {
            console.error(`Error deleting group ${groupName}:`, error);
            throw error;
        }
    }

    // Group disabling methods
    async disableGroup(groupName: string) {
        console.log(`Disabling group: ${groupName}`);
        try {
            await this.searchForGroup(groupName);
            await this.page.locator(`text="${groupName}"`).first().click();
            await this.disableGroupButton.click();
            await this.page.getByRole('button', { name: 'Disable' }).click();
            const Message = await this.waitForSuccess();
            return Message;
        } catch (error) {
            console.error(`Error deleting group ${groupName}:`, error);
            throw error;
        }
    }

    // Utility methods
    async getGroupsCount(): Promise<number> {
        const count = await this.groupRows.count();
        console.log(`Found ${count} groups`);
        return count;
    }

    async isGroupPresent(groupName: string): Promise<boolean> {
        let flag: boolean;
        const count = await this.page.locator(`text="${groupName}"`).count();
        if (count > 0) {
            console.log(`Group ${groupName} is present`);
            flag = true;
        } else {
            console.log(`Group ${groupName} is not present`);
            flag = false;
        }
        return flag;
    }

    async getGroupStatus(groupName: string): Promise<string> {
        await this.searchForGroup(groupName);
        const statusCell = this.page.locator(`//td[contains(text(), "${groupName}")]/following-sibling::td[contains(@class, "status-cell")]`);
        return await statusCell.textContent() || '';
    }

    async verifyGrid() {
        await this.cc.verifyGridHeader([
            'Name',
            'Description',
            'Status',
            'Users',
            'Created By',
            'Date created',
            'Modified By',
            'Date modified',
            'Action'
        ]);
    }



    // Advanced methods for testing
    async createMultipleGroups(count: number, namePrefix: string = 'Test Group'): Promise<string[]> {
        console.log(`Creating ${count} test groups`);
        const groupNames: string[] = [];

        for (let i = 0; i < count; i++) {
            const groupName = `${namePrefix} ${Date.now()}-${i}`;
            await this.clickButtonAddGroup();
            // await this.createGroup({
            //     groupName: groupName,
            //       groupDescription: `Auto-generated test group ${i}`,
            //       groupLevels: ['Store']
            //  });
            groupNames.push(groupName);

            // Wait a bit between creations to avoid rate limiting
            await this.page.waitForTimeout(500);
        }

        return groupNames;
    }

    // async deleteAllTestGroups(namePrefix: string = 'Test Group') {
    //     console.log(`Deleting all test groups with prefix: ${namePrefix}`);
    //    await this.searchForGroup(namePrefix);

    //    const count = await this.groupRows.count();
    //     console.log(`Found ${count} groups to delete`);

    //    for (let i = 0; i < count; i++) {
    //      // Always delete the first row since the list will shift up after each deletion
    //        await this.groupRows.first().click();
    //        await this.deleteGroupButtonClick();
    //      await this.confirmDeleteGroup();

    // Wait a bit between deletions
    //     await this.page.waitForTimeout(500);
    //   }
    //  }

    // Mock methods for testing error scenarios
    async setupServerErrorMock(endpoint: string = '**/api/groups') {
        await this.page.route(endpoint, route => {
            return route.fulfill({
                status: 500,
                body: JSON.stringify({ error: 'Internal Server Error' })
            });
        });
    }

    async setupNetworkTimeoutMock(endpoint: string = '**/api/groups') {
        await this.page.route(endpoint, route => {
            // Never resolve the request
            return new Promise(() => { });
        });
    }

    async resetMocks() {
        await this.page.unrouteAll();
    }

    // Accessibility testing methods
    async checkAccessibility() {
        // This would integrate with an accessibility testing library
        // For example, using axe-core
        const accessibilityViolations = await this.page.evaluate(() => {
            // @ts-ignore
            return window.axe.run();
        });

        return accessibilityViolations;
    }

    // Performance testing methods
    async measurePageLoadTime(): Promise<number> {
        const startTime = Date.now();
        await this.clickDashboardPageLink(); // Navigate away
        await this.clickGroupsPageLink(); // Navigate back
        await expect(this.btnAddGroup).toBeVisible();
        return Date.now() - startTime;
    }

    // Helper methods for test data management
    async storeTestData(key: string, value: any) {
        await this.page.evaluate(
            ([k, v]) => { localStorage.setItem(`test_${k}`, JSON.stringify(v)); },
            [key, value]
        );
    }

    async retrieveTestData(key: string): Promise<any> {
        const value = await this.page.evaluate(
            (k) => { return localStorage.getItem(`test_${k}`); },
            key
        );
        return value ? JSON.parse(value) : null;
    }

    async clearTestData(key: string) {
        await this.page.evaluate(
            (k) => { localStorage.removeItem(`test_${k}`); },
            key
        );
    }
    // Helper Methods
    async waitForSuccess(): Promise<string> {
        await this.successMessage.waitFor({ state: 'visible', timeout: 10000 });
        const message = await this.successMessage.textContent() || '';
        console.log(`Success message: "${message}"`);
        await this.closeSuccessMessage();
        return message;
    }
    async closeSuccessMessage() {
        await this.successMessage.waitFor({ state: 'visible', timeout: 1000 });
        await this.successMessage.click();
    }


    async findLocator() {
        await this.page.locator('div', { hasText: 'Groups List' });
    }
 
async copyGroup(groupName: string) {
    console.log(`Copying group: ${groupName}`);
    try {
         let message = '';
        // Search for the group first
        await this.searchForGroup(groupName);
        // Find and click the copy button for this group
        await this.copyGroupButton.click();
        // The copy form should be pre-filled with the original group's data
        // Just click save to create the copy
        await this.page.waitForTimeout(2000);
        await this.clickButtonSaveAddGroupForm();
        // Wait for success message
        message = await this.waitForSuccess();
        console.log('Success Message: ' + message);
        return message;
    } catch (error) {
        console.error(`Error copying group ${groupName}:`, error);
        throw error;
    }
}






    async verifyGridd() {
        await this.cc.verifyGridHeader([
            'Name',
            'Description',
            'Status',
            'Users',
            'Created By',
            'Date created',
            'Modified By',
            'Date modified',
            'Action'
        ]);
    }

    // async deleteGroupButtonClickd() {
    //    this.deleteGroupIconButton = await this.page.locator("//mat-icon[normalize-space()='delete']").first();
    //   await this.deleteGroupIconButton.click();
    //    this.deleteGroupButton = await this.page.getByRole("button", { name: "Delete", exact: true }).nth(0);
    //   this.cancelButton = await this.page.getByRole("button", { name: "Cancel" });
    //    await this.page.locator("pag-delete-group");
    //   await expect(
    //      this.page.locator("mat-label", {
    //             hasText: "Delete group",
    //        })
    //    ).toHaveText("Delete group");
    //      await this.deleteGroupButton.click();
    //  }

    async fillDeleteGroupFormData(formData: { id: string }) {
        if (formData.id) {
            this.deleteGroupButton = await this.page.getByRole('button', { name: /Delete/i, exact: true });
            await expect(this.deleteGroupButton).toBeVisible();
            await this.deleteGroupButton.click();
        }
    }
}
