import { Locat<PERSON>, <PERSON> } from "@playwright/test";

export default class DefaltDashboardCustomization {
    private page:Page;

        public ChkboxTask: Locator;
    

        constructor(page: Page) {
            this.page = page;
            this.pageElements();
        }

        async pageElements(){
            this.ChkboxTask = this.page.locator("//input[@id='mat-mdc-checkbox-6-input']");
         
       
        }
}