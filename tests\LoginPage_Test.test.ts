import { Browser, chromium, expect, Page, test } from "@playwright/test";
import LoginPage from "../pages/loginPage";
import DashboardPage from "../pages/dashboardPage";

// Import test data
const testdata = JSON.parse(JSON.stringify(require("../testData.json")));
// Define variables at the top level
let browser: Browser;
let page: Page;
let loginPage: LoginPage;
let dashboardPage: DashboardPage;

test.describe('Login Page Tests', () => {
    // Setup before all tests
    test.beforeAll(async () => {
        // Set a longer timeout for the beforeAll hook
        test.setTimeout(60000);
        console.log("Starting test suite setup for Login Page Tests");
        try {
            // Launch browser with specific configuration
            browser = await chromium.launch({ headless: false });
            console.log('Browser launched successfully');
        } catch (error) {
            console.error("Error launching browser:", error);
            throw error;
        }
    });
    // Setup before each test
    test.beforeEach(async ({ baseURL }, testInfo) => {
        console.log(`========== STARTING TEST: ${testInfo.title} ==========`);
        try {
            // Create a new page for each test
            page = await browser.newPage({});
            // Navigate to the application
            console.log(`Navigating to base URL: ${baseURL}`);
            await page.goto(`${baseURL}`, {
                waitUntil: 'networkidle',
                timeout: 30000
            });

            // Initialize page objects
            console.log('Initializing page objects...');
            loginPage = new LoginPage(page);
            dashboardPage = new DashboardPage(page);

            // Initialize page elements
            await loginPage.initializeElements();
            await dashboardPage.initializeElements();

            console.log('Test setup completed');
        } catch (error) {
            console.error(`Error in beforeEach for test "${testInfo.title}":`, error);
        }
    });
    // Test cases
    test.describe.serial("Navihgation And Basic UI Elements", () => {
        test('should display correct page title', async () => {
            // Verify the page title
            const expectedTitle = "Sign in to demand-platform";
            const actualTitle = await loginPage.getPageTitle();
            console.log(`Expected title: "${expectedTitle}", Actual title: "${actualTitle}"`);
            await expect(actualTitle).toBe(expectedTitle);
        });
        test('should display correct page URL', async () => {
            // Verify the page URL
            await expect(page).toHaveURL(/.*login/);
        });
        test('should display all login page elements', async () => {
            // Verify all login page elements are visible
            await expect(loginPage.IFUserName).toBeVisible();
            await expect(loginPage.IFPassword).toBeVisible();
            await expect(loginPage.BtnLogin).toBeVisible();
            await expect(loginPage.ChkBxRememberMe).toBeVisible();
        });
        test('should verify all login page elements are enabled', async () => {
            // Verify all login page elements are enabled
            await expect(loginPage.IFUserName).toBeEnabled();
            await expect(loginPage.IFPassword).toBeEnabled();
            await expect(loginPage.BtnLogin).toBeEnabled();
            await expect(loginPage.ChkBxRememberMe).toBeEnabled();
        });
         test('should handle special characters in login fields', async () => {
            // Test with special characters
            const specialCharsUsername = 'test@#$%^&*()';
            const specialCharsPassword = 'pass@#$%^&*()';

            await loginPage.enterUserName(specialCharsUsername);
            await loginPage.enterPassword(specialCharsPassword);

            // Verify fields contain the special characters
            await expect(loginPage.IFUserName).toHaveValue(specialCharsUsername);
            await expect(loginPage.IFPassword).toHaveValue(specialCharsPassword);
            console.log('Special characters handled correctly in form fields');
        });
    });
    test.describe.serial("Positive Test Cases (Valid Inputs)", () => {
        test('should successfully login with valid credentials', async () => {
            // Perform login with valid credentials
            await loginPage.loginProcess(testdata.username, testdata.password);
            // Verify successful login by checking for logout button
            await expect(dashboardPage.logoutButton).toBeVisible({ timeout: 10000 });
            console.log('Successfully logged in and verified dashboard access');
        });
        test('should successfully login with valid credentials - case-insensitive username', async () => {
            // Perform login with valid credentials
            await loginPage.loginProcess(testdata.username.toLowerCase(), testdata.password);
            // Verify successful login by checking for logout button
            await expect(dashboardPage.logoutButton).toBeVisible({ timeout: 10000 });
            console.log('Successfully logged in and verified dashboard access');
        });
    });
    test.describe.serial("Negative Test Cases (Invalid Inputs)", () => {
        test('should not login with invalid username', async () => {
            // Perform login with invalid username
            await loginPage.loginProcess('<EMAIL>', testdata.password);
// Verify error message is displayed
            const errorMessage = await loginPage.getErrorMessage();
            console.log(`Error message displayed: "${errorMessage}"`);

            await expect(page.locator('text=Invalid username or password.')).toBeVisible();
        });

        test('should not login with empty username and password', async () => {
            // Perform login with empty username and password
            await loginPage.loginProcess('', '');

            // Verify error message is displayed
            const errorMessage = await loginPage.getErrorMessage();
            console.log(`Error message displayed: "${errorMessage}"`);

            await expect(page.locator('text=Invalid username or password.')).toBeVisible();
        });
        test('should not login with empty password', async () => {
            // Perform login with empty password
            await loginPage.loginProcess(testdata.username, '');

            // Verify error message is displayed
            const errorMessage = await loginPage.getErrorMessage();
            console.log(`Error message displayed: "${errorMessage}"`);

            await expect(page.locator('text=Invalid username or password.')).toBeVisible();
        });
        test('should not login with empty username', async () => {
            // Perform login with empty username
            await loginPage.loginProcess('', testdata.password);

            // Verify error message is displayed
            const errorMessage = await loginPage.getErrorMessage();
            console.log(`Error message displayed: "${errorMessage}"`);

            await expect(page.locator('text=Invalid username or password.')).toBeVisible();
        });

        test('should not login with username with only spaces', async () => {
            // Perform login with username with only spaces
            await loginPage.loginProcess('   ', testdata.password);

            // Verify error message is displayed
            const errorMessage = await loginPage.getErrorMessage();
            console.log(`Error message displayed: "${errorMessage}"`);

            await expect(page.locator('text=Invalid username or password.')).toBeVisible();
        });

        test('should show error with invalid credentials', async () => {
            // Perform login with invalid credentials
            await loginPage.loginProcess('<EMAIL>', 'wrongpassword');

            // Verify error message is displayed
            const errorMessage = await loginPage.getErrorMessage();
            console.log(`Error message displayed: "${errorMessage}"`);

            await expect(page.locator('text=Invalid username or password.')).toBeVisible();
        });
    });
    test.describe.serial("Form Validation and Remember Me Functionality", () => {
        test('should validate empty form submission', async () => {
            // Submit form without entering credentials
            await loginPage.clickLoginBtn();

            // Verify error message is displayed
            await expect(page.locator('text=Invalid username or password.')).toBeVisible();
            console.log('Empty form validation working correctly');
        });

        test('should handle remember me checkbox', async () => {
            // Check the remember me checkbox
            await loginPage.checkboxRememberCheckUncheck('check');
            await expect(loginPage.ChkBxRememberMe).toBeChecked();
            console.log('Remember me checkbox checked successfully');

            // Uncheck the remember me checkbox
            await loginPage.checkboxRememberCheckUncheck('uncheck');
            await expect(loginPage.ChkBxRememberMe).not.toBeChecked();
            console.log('Remember me checkbox unchecked successfully');
        });

        test('should maintain form data after failed login', async () => {
            // Enter username and wrong password
            const testUsername = testdata.username;
            await loginPage.enterUserName(testUsername);
            await loginPage.enterPassword('wrongpassword');
            await loginPage.clickLoginBtn();

            // Verify username is still present after failed login
            await expect(loginPage.IFUserName).toHaveValue(testUsername);
            console.log('Username persisted after failed login attempt');
        });

       

    

        test('should toggle password visibility', async () => {
            // Enter a password
            const testPassword = 'TestPassword123';
            await loginPage.enterPassword(testPassword);

            // Check initial password field type (should be password/hidden)
            const initialType = await loginPage.IFPassword.getAttribute('type');
            expect(initialType).toBe('password');
            console.log('Password field initially has type "password"');

            // Click show password button if it exists
            if (await loginPage.ShowPasswordButton.isVisible()) {
                await loginPage.ShowPasswordButton.click();

                // Verify password is now visible
                const newType = await loginPage.IFPassword.getAttribute('type');
                expect(newType).toBe('text');
                console.log('Password visibility toggled successfully');

                // Toggle back
                await loginPage.ShowPasswordButton.click();
                const finalType = await loginPage.IFPassword.getAttribute('type');
                expect(finalType).toBe('password');
                console.log('Password visibility toggled back successfully');
            } else {
                console.log('Show password button not available, skipping toggle test');
            }
        });
    });
    // Cleanup after each test
    test.afterEach(async ({ }, testInfo) => {
        console.log(`========== FINISHING TEST: ${testInfo.title} ==========`);

        // Log test status
        if (testInfo.status === 'passed') {
            console.log(`✅ TEST PASSED: ${testInfo.title}`);
        } else if (testInfo.status === 'failed') {
            console.log(`❌ TEST FAILED: ${testInfo.title}`);

            // Take screenshot on failure
            try {
                const screenshotPath = `./test-results/failures/login-${testInfo.title.replace(/\s+/g, '-')}-${Date.now()}.png`;
                await page.screenshot({ path: screenshotPath, fullPage: true });
                console.log(`Failure screenshot saved to: ${screenshotPath}`);
            } catch (screenshotError) {
                console.error('Failed to take failure screenshot:', screenshotError);
            }
        }

        // Close the page after each test
        try {
            await page.close();
            console.log('Page closed successfully');
        } catch (error) {
            console.error('Error closing page:', error);
        }

        console.log('='.repeat(60));
    });

    // Cleanup after all tests
    test.afterAll(async () => {
        console.log('Starting Login Page test suite cleanup...');

        try {
            // Close browser
            await browser.close();
            console.log('Browser closed successfully');
        } catch (error) {
            console.error('Error closing browser:', error);
        }

        console.log('Login Page test suite cleanup completed');
    });
});
