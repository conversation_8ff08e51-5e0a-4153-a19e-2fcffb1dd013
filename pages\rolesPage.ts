import { Locator, <PERSON> } from "@playwright/test";

export default class RolesPage {
    private page:Page;

        public BtnAddRole: Locator;
    

        constructor(page: Page) {
            this.page = page;
            this.pageElements();
        }

        async pageElements(){
            this.BtnAddRole = this.page.locator("//span[@class='mdc-button__label'][normalize-space()='Add Role']");
         
       
        }
}