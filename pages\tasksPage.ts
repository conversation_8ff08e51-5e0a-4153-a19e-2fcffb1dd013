import { expect, Locator, <PERSON> } from "@playwright/test";
import { CommonCase } from "./common-case";

export default class TasksPage {
    private page: Page;
    private createTaskButton: Locator;
    private allTaskButton: Locator;
    private overdueTaskButton: Locator;
    private createdTaskButton: Locator;
    private subjectInput: Locator;
    private messageInput: Locator;
    private demandPlanLink: Locator;
    private assignToInput: Locator;
    private cc: CommonCase;

    public BtnCreateTask: Locator;
    public BtnAll: Locator;
    public BtnOverdue: Locator;
    public BtnCreated: Locator;



    constructor(page: Page) {
        this.page = page;
        this.initializeElements();
    }

    async initializeElements(){
        this.BtnCreateTask = this.page.locator("//span[normalize-space()='Create Task']");
        this.BtnAll = this.page.locator("//span[normalize-space()='All']");
        this.BtnOverdue = this.page.locator("//span[normalize-space()='Overdue']");
        this.BtnCreated = this.page.locator("//span[normalize-space()='Created']");
     }



    async navigateToTaskPage() {
        await this.page.goto("http://localhost:4123/#/tasks");
        await this.page.locator('[href*="#/tasks"]').click();
    }

    async verifyPageTitle() {
        this.cc = new CommonCase(this.page);
        await this.cc.verifyPageTitle("Task");
    }

    async verifyGrid() {
        await this.cc.verifyGridHeader([
            "Name",
            "Assigned to",
            "Status",
            "Created By",
            "Date Created",
            "Due Date",
            "Action",
        ]);
    }

    async findLocator() {
        this.allTaskButton = await this.page.getByRole("button", { name: "All" });
        this.overdueTaskButton = await this.page.getByRole("button", { name: "Overdue", });
        this.createdTaskButton = await this.page.getByRole("button", { name: "Created", });

        await this.page.locator("span", { hasText: "Task List" });
    }

    async createTaskButtonClick() {
        this.createTaskButton = await this.page.getByRole("button", { name: "Create Task", });
        await this.createTaskButton.click();
        await this.page.locator("pag-create-issue");
        await expect(this.page.locator("span", { hasText: "New Task", })).toHaveText("New Task");
        this.subjectInput = await this.page.getByRole("textbox", { name: "Add Subject", });
        this.messageInput = await this.page.locator("quill-editor div").nth(1);
        this.demandPlanLink = await this.page.locator("input#link");
        this.assignToInput = await this.page.getByRole("combobox", { name: "Add Assigned To" }).locator("path");
    }

    async fillFormData(formData: {
        subject: string;
        message: string;
        demandPlanLink?: string;
        searchText: string;
        calendarData: string;
    }) {
        await this.subjectInput.fill(formData.subject);
        await this.page.getByRole("paragraph").click();
        await this.messageInput.fill(formData.message);

        if (formData.demandPlanLink) {
            await this.subjectInput.fill(formData.demandPlanLink);
        }

        await this.page
            .getByRole("combobox", { name: "Add Assigned To" })
            .locator("path")
            .click();

        await this.page.getByRole("option", { name: formData.searchText }).click();

        await this.page.getByRole("button", { name: "Open calendar" }).click();

        await this.page
            .getByRole("button", { name: formData.calendarData })
            .click();

        await this.page
            .getByRole("button", {
                name: "Create",
            })
            .click();

        await this.page.getByRole("button", { name: "Dismiss" }).click();
    }

    async verifyNewAddedRow(formData: { subject: string; searchText: string }) {
        await expect(this.page.locator(".ag-cell").nth(0)).toContainText(
            formData.subject
        );
        await expect(this.page.locator(".ag-cell").nth(1)).toHaveText(
            formData.searchText
        );
        await expect(this.page.locator(".ag-cell").nth(2)).toHaveText("Open");
        await expect(this.page.locator(".ag-cell").nth(3)).toHaveText(
            formData.searchText
        );
    }

    async navigateToNewAddedRow(subject: string) {
        await this.page
            .getByRole("gridcell", { name: new RegExp(subject) })
            .first()
            .click();

        await this.page
            .getByRole("button")
            .filter({ hasText: "more_vert" })
            .click();

        await this.page.getByRole("menuitem", { name: "Delete" }).click();

        await this.page.getByRole("button", { name: "Dismiss" }).click();
    }
}








