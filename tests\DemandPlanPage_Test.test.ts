import { Browser, expect, Page, test } from "@playwright/test";
import { webkit, chromium, firefox } from "@playwright/test";

import DashboardPage from "../pages/dashboardPage";
import LoginPage from "../pages/loginPage";
import DemandPlanPage from "../pages/demandPlanPage";
const testdata = JSON.parse(JSON.stringify(require("..//testData.json")));

let page: Page;
let dashboardPage;
let loginPage;
let demandPlanPage;
let expectedSuccessMessage: string;
let actualSuccessMessage: string;
let expectedPageTitle: string;
let txtviewname: string;
let txtviewdescription: string;
let ExpectedSuccessMessageDisplayedStatus: boolean;
let isChkboxChecked: string;
let txtTaskSubject: string;
let txtTaskMessage: string;
let chkBoxAssignedTo: string;
let dtDueDate: string;
let txtDraftname: string;
let txtDraftDescription: string;
let currentCheckedStatus: boolean;

test.describe('Demand Plan Page Tests', () => {
 //-------------------Before All------------------------------------
    test.beforeAll(async ({ baseURL }) => {
        console.log("Starting test suite setup for Demand Plan Page Tests");
        try {
            const browser: Browser = await chromium.launch({ headless: true });
            page = await browser.newPage();
            await page.goto(`${baseURL}`);
            // Initialize page objects
            console.log('Initializing page objects...');
            dashboardPage = new DashboardPage(page);
            loginPage = new LoginPage(page);
            demandPlanPage = new DemandPlanPage(page);
            //Initialize page elements
            await loginPage.initializeElements();
            await dashboardPage.initializeElements();
            await demandPlanPage.initializeElements();
            // Perform login before all tests
            console.log("Performing login...");
            await loginPage.BeforeEachloginProcess();
            console.log("Login completed");
            console.log('Demand Plan test suite setup completed');
        } catch (error) {
            console.error("Error in Demand Plan Page beforeAll setup:", error);
        }
    });
    test.beforeEach(async ({ }, testInfo) => {
        console.log(`========== STARTING TEST: ${testInfo.title} ==========`);
        // Reset all test variables to initial state
        expectedPageTitle = "";
        expectedSuccessMessage = "";
        actualSuccessMessage = "";
        txtviewname = "";
        txtviewdescription = "";
        ExpectedSuccessMessageDisplayedStatus = false;
        isChkboxChecked = "";
        txtTaskSubject = "";
        txtTaskMessage = "";
        chkBoxAssignedTo = "";
        dtDueDate = "";
        txtDraftname = "";
        txtDraftDescription = "";
        currentCheckedStatus = false;
        // Navigate to Demand Plan page and ensure it's fully loaded
        console.log('Navigating to Demand Plan page...');
        await dashboardPage.clickDemandPlanPageLink();
        await expect(demandPlanPage.demandPlanPageLabel).toBeVisible({ timeout: 15000 });
        // Set up test-specific prerequisites based on test title
        console.log(`Setting up prerequisites for test: ${testInfo.title}`);
        try {
            // Draft-related test setup
            if (testInfo.title === 'should delete draft successfully'
                || testInfo.title === 'should restore draft successfully'
                || testInfo.title === 'should view restored draft successfully'
                || testInfo.title === 'should close restored draft successfully') {
                console.log(`Creating draft for test: ${testInfo.title}`);
                expectedSuccessMessage = '';
                expectedSuccessMessage = 'Draft Saved';
                actualSuccessMessage = '';
                await demandPlanPage.clickButtonAddDraft();
                actualSuccessMessage = await demandPlanPage.createDraft(testdata.txtDraftname, testdata.txtDraftDescription);
                actualSuccessMessage = actualSuccessMessage.trim();
                console.log('Actual Success Message: ' + actualSuccessMessage);
                console.log('Expected Success Message: ' + expectedSuccessMessage);
                expect(actualSuccessMessage).toBe(expectedSuccessMessage);
                // Wait for success message to disappear
                await page.waitForTimeout(1000);
                // Verify draft was created
                await demandPlanPage.openDraftsDrawer();
                const draftExists = await page.isVisible(`text="${testdata.txtDraftname}"`, { timeout: 5000 });
                expect(draftExists).toBeTruthy();
                await demandPlanPage.closeDraftDrawer();
            }
        } catch (setupError) {
            console.error(`Error during test setup for ${testInfo.title}:`, setupError);
            // Try to recover by reloading the page
            console.log('Attempting to recover from setup failure by reloading page...');
            await page.reload();
        }
        console.log(`Test setup completed for: ${testInfo.title}`);
    });

    test.describe.serial("Navigation and Basic UI Elements", () => {
        test("should have correct page label text", async () => {
            // const pageLabelText = "Demand Plan";
            const pageLabelText = "Main";
            await expect(demandPlanPage.demandPlanPageLabel).toHaveText(
                pageLabelText
            );
        });
        test("should have correct page title", async () => {
            const expectedTitle = "Demand Intelligence";
            const actualTitle = await dashboardPage.getPageTitle(expectedTitle);
            await expect(actualTitle).toBe(expectedTitle);
        });
        test("should have correct page URL", async () => {
            await expect(page).toHaveURL(/.*demand-plan/);
        });
        test("should have all required UI elements visible", async () => {
            await expect(demandPlanPage.dashboardPageLink).toBeVisible();
            await expect(demandPlanPage.demandPlanPageLink).toBeVisible();
            await expect(demandPlanPage.tasksPageLink).toBeVisible();
            await expect(demandPlanPage.usersPageLink).toBeVisible();
            await expect(demandPlanPage.rolesPageLink).toBeVisible();
            await expect(demandPlanPage.groupsPageLink).toBeVisible();
            await expect(demandPlanPage.hypercubePageLink).toBeVisible();
            await expect(demandPlanPage.openHypercubeDrawerButton).toBeVisible();
            await expect(demandPlanPage.toggleDarkModeButton).toBeVisible();
            await expect(demandPlanPage.openNotificationDrawerButton).toBeVisible();
            await expect(demandPlanPage.logoutButton).toBeVisible();
            await expect(demandPlanPage.userProfileButton).toBeVisible();
            await expect(demandPlanPage.demandPlanPageLabel).toBeVisible();
            await expect(demandPlanPage.openSavedViewsListButton).toBeVisible();
            await expect(demandPlanPage.openGraphSectionButton).toBeVisible();
            await expect(demandPlanPage.openAddViewFormButton).toBeVisible();
            await expect(demandPlanPage.openSavedViewsSettingFormButton).toBeVisible();
            await expect(demandPlanPage.expandLessButton).toBeVisible();
            await expect(demandPlanPage.openDraftsDrawerButton).toBeVisible();
            await expect(demandPlanPage.exportToExcelExpandMore).toBeVisible();
            await expect(demandPlanPage.openAddTaskFormButton).toBeVisible();
            await expect(demandPlanPage.openAddDraftFormButtonFromDemandPlanPage).toBeVisible();
            await expect(demandPlanPage.fullScreenViewButton).toBeVisible();
            await expect(demandPlanPage.openCopyLinkUrlFormButton).toBeVisible();
            await expect(demandPlanPage.lockRowButton).toBeVisible();
        });
    });
    test.describe.serial("View Management", () => {
        test("should open Add View Form successfully", async ({ }) => {
            await demandPlanPage.openAddViewForm();
            await expect(demandPlanPage.viewNameInput).toBeVisible();
            await expect(demandPlanPage.viewDescriptionInput).toBeVisible();
            await expect(demandPlanPage.createViewButton).toBeVisible();
            await expect(demandPlanPage.globalViewCheckbox).toBeVisible();
        });
        test("should close Add View Form successfully by clicking close form button", async ({ }) => {
            await demandPlanPage.openAddViewForm();
            await demandPlanPage.clickButtonCloseWindowAddViewForm();
            await expect(demandPlanPage.viewNameInput).not.toBeVisible();
            await expect(demandPlanPage.viewDescriptionInput).not.toBeVisible();
            await expect(demandPlanPage.createViewButton).not.toBeVisible();
            await expect(demandPlanPage.globalViewCheckbox).not.toBeVisible();
        });
        test("should close Add View Form successfully by clicking cancel button", async ({ }) => {
            await demandPlanPage.openAddViewForm();
            await demandPlanPage.clickButtonCancelAddDraftForm();
            await expect(demandPlanPage.viewNameInput).not.toBeVisible();
            await expect(demandPlanPage.viewDescriptionInput).not.toBeVisible();
            await expect(demandPlanPage.createViewButton).not.toBeVisible();
            await expect(demandPlanPage.globalViewCheckbox).not.toBeVisible();
        });
        test("should disabled create view button when view name is empty", async ({ }) => {
            await demandPlanPage.openAddViewForm();
            await expect(demandPlanPage.createViewButton).toBeDisabled();
        });
        test("should enabled create view button when view name is entered", async ({ }) => {
            await demandPlanPage.openAddViewForm();
            await demandPlanPage.enterViewName(testdata.txtviewnamelocal);
            await expect(demandPlanPage.createViewButton).toBeEnabled();
        });
        test("should disabled create view button when view name is entered and then deleted", async ({ }) => {
            await demandPlanPage.openAddViewForm();
            await demandPlanPage.enterViewName(testdata.txtviewnamelocal);
            await demandPlanPage.enterViewName("");
            await expect(demandPlanPage.createViewButton).toBeDisabled();
        });
        test("should create and delete local view successfully", async ({ }) => {
            expectedSuccessMessage = 'View Saved';
            await demandPlanPage.openAddViewForm();
            actualSuccessMessage = await demandPlanPage.createView(
                testdata.txtviewnamelocal,
                testdata.txtviewdescription,
                testdata.isChkboxCheckedLocal
            );
            actualSuccessMessage = actualSuccessMessage.trim();
            console.log('Actual Success Message: ' + actualSuccessMessage);
            console.log('Expected Success Message: ' + expectedSuccessMessage);
            expect(actualSuccessMessage).toBe(expectedSuccessMessage);
            await page.waitForTimeout(4000);
            expectedSuccessMessage = '';
            expectedSuccessMessage = 'View Deleted';
            actualSuccessMessage = '';
            actualSuccessMessage = await demandPlanPage.deleteSavedView();
            actualSuccessMessage = actualSuccessMessage.trim();
            console.log('Actual Success Message: ' + actualSuccessMessage);
            console.log('Expected Success Message: ' + expectedSuccessMessage);
            expect(actualSuccessMessage).toBe(expectedSuccessMessage);
        });
        test("should create and delete global view successfully", async ({ }) => {
            await demandPlanPage.openAddViewForm();
            await demandPlanPage.createView(
                testdata.txtviewnameglobal,
                testdata.txtviewdescription,
                testdata.isChkboxCheckedGlobal
            );
            await expect(demandPlanPage.successMessage).toBeVisible();
            await page.waitForTimeout(4000);
            await demandPlanPage.deleteSavedView();
            await expect(demandPlanPage.successMessage).toBeVisible();
        });
    });
    test.describe.serial("Draft Management", () => {
        test("should open Drafts Drawer successfully", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await expect(demandPlanPage.addDraftButtonFromDraftsDrawer).toBeVisible();
            await expect(demandPlanPage.closeDraftsDrawerButton).toBeVisible();
            await expect(demandPlanPage.draftsDrawer).toBeVisible();
            await expect(demandPlanPage.draftsLabel).toBeVisible();
        });
        test("should close Drafts Drawer successfully", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.closeDraftDrawer();
            await expect(
                demandPlanPage.addDraftButtonFromDraftsDrawer
            ).not.toBeVisible();
            await expect(demandPlanPage.closeDraftsDrawerButton).not.toBeVisible();
            await expect(demandPlanPage.draftsDrawer).not.toBeVisible();
            await expect(demandPlanPage.draftsLabel).not.toBeVisible();
        });
        test("should open save Draft Form successfully from Drafts Drawer", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await expect(demandPlanPage.addDraftlabel).toBeVisible();
            await expect(demandPlanPage.closeAddDraftsFormButton).toBeVisible();
            await expect(demandPlanPage.draftNamelabel).toBeVisible();
            await expect(demandPlanPage.draftNameInput).toBeVisible();
            await expect(demandPlanPage.draftDescriptionlabel).toBeVisible();
            await expect(demandPlanPage.draftDescriptionInput).toBeVisible();
            await expect(demandPlanPage.cancelAddDraftFormButton).toBeVisible();
            await expect(demandPlanPage.createDraftButton).toBeVisible();
        });
        test("should open save Draft Form successfully from Demand Plan Page", async ({ }) => {
            await demandPlanPage.clickButtonAddDraft();
            await expect(demandPlanPage.addDraftlabel).toBeVisible();
            await expect(demandPlanPage.closeAddDraftsFormButton).toBeVisible();
            await expect(demandPlanPage.draftNamelabel).toBeVisible();
            await expect(demandPlanPage.draftNameInput).toBeVisible();
            await expect(demandPlanPage.draftDescriptionlabel).toBeVisible();
            await expect(demandPlanPage.draftDescriptionInput).toBeVisible();
            await expect(demandPlanPage.cancelAddDraftFormButton).toBeVisible();
            await expect(demandPlanPage.createDraftButton).toBeVisible();
        });
        test("should close save Draft Form successfully by clicking close form button, navigated using Demand Plan Page", async ({ }) => {
            await demandPlanPage.clickButtonAddDraft();
            await demandPlanPage.clickButtonCloseWindowAddDraftForm();
            await expect(demandPlanPage.addDraftlabel).not.toBeVisible();
            await expect(demandPlanPage.closeAddDraftsFormButton).not.toBeVisible();
            await expect(demandPlanPage.draftNamelabel).not.toBeVisible();
            await expect(demandPlanPage.draftNameInput).not.toBeVisible();
            await expect(demandPlanPage.draftDescriptionlabel).not.toBeVisible();
            await expect(demandPlanPage.draftDescriptionInput).not.toBeVisible();
            await expect(demandPlanPage.cancelAddDraftFormButton).not.toBeVisible();
            await expect(demandPlanPage.createDraftButton).not.toBeVisible();
        });
        test("should close save Draft Form successfully from Demand Plan Page by clicking close form button, navigated using Drafts Drawer", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await demandPlanPage.clickButtonCloseWindowAddDraftForm();
            await expect(demandPlanPage.addDraftlabel).not.toBeVisible();
            await expect(demandPlanPage.closeAddDraftsFormButton).not.toBeVisible();
            await expect(demandPlanPage.draftNamelabel).not.toBeVisible();
            await expect(demandPlanPage.draftNameInput).not.toBeVisible();
            await expect(demandPlanPage.draftDescriptionlabel).not.toBeVisible();
            await expect(demandPlanPage.draftDescriptionInput).not.toBeVisible();
            await expect(demandPlanPage.cancelAddDraftFormButton).not.toBeVisible();
            await expect(demandPlanPage.createDraftButton).not.toBeVisible();
            await expect(demandPlanPage.addDraftButtonFromDraftsDrawer).toBeVisible();
            await expect(demandPlanPage.closeDraftsDrawerButton).toBeVisible();
            await expect(demandPlanPage.draftsDrawer).toBeVisible();
            await expect(demandPlanPage.draftsLabel).toBeVisible();
        });
        test("should close save Draft Form successfully by clicking cancel button,navigated using Demand Plan Page", async ({ }) => {
            await demandPlanPage.clickButtonAddDraft();
            await demandPlanPage.clickButtonCancelAddDraftForm();
            await expect(demandPlanPage.addDraftlabel).not.toBeVisible();
            await expect(demandPlanPage.closeAddDraftsFormButton).not.toBeVisible();
            await expect(demandPlanPage.draftNamelabel).not.toBeVisible();
            await expect(demandPlanPage.draftNameInput).not.toBeVisible();
            await expect(demandPlanPage.draftDescriptionlabel).not.toBeVisible();
            await expect(demandPlanPage.draftDescriptionInput).not.toBeVisible();
            await expect(demandPlanPage.cancelAddDraftFormButton).not.toBeVisible();
            await expect(demandPlanPage.createDraftButton).not.toBeVisible();
        });
        test("should close save Draft Form successfully from Demand Plan Page by clicking cancel button, navigated using Drafts Drawer", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await demandPlanPage.clickButtonCancelAddDraftForm();
            await expect(demandPlanPage.addDraftlabel).not.toBeVisible();
            await expect(demandPlanPage.closeAddDraftsFormButton).not.toBeVisible();
            await expect(demandPlanPage.draftNamelabel).not.toBeVisible();
            await expect(demandPlanPage.draftNameInput).not.toBeVisible();
            await expect(demandPlanPage.draftDescriptionlabel).not.toBeVisible();
            await expect(demandPlanPage.draftDescriptionInput).not.toBeVisible();
            await expect(demandPlanPage.cancelAddDraftFormButton).not.toBeVisible();
            await expect(demandPlanPage.createDraftButton).not.toBeVisible();
            await expect(demandPlanPage.addDraftButtonFromDraftsDrawer).toBeVisible();
            await expect(demandPlanPage.closeDraftsDrawerButton).toBeVisible();
            await expect(demandPlanPage.draftsDrawer).toBeVisible();
            await expect(demandPlanPage.draftsLabel).toBeVisible();
        });
        test("should disabled create draft button when draft name is empty - navigated using Demand Plan Page", async ({ }) => {
            await demandPlanPage.clickButtonAddDraft();
            await expect(demandPlanPage.createDraftButton).toBeDisabled();
        });
        test("should disabled create draft button when draft name is empty - navigated using Drafts Drawer", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await expect(demandPlanPage.createDraftButton).toBeDisabled();
        });
        test("should enabled create draft button when draft name is entered - navigated using Demand Plan Page", async ({ }) => {
            await demandPlanPage.clickButtonAddDraft();
            await demandPlanPage.enterDraftName(testdata.txtDraftname);
            await expect(demandPlanPage.createDraftButton).toBeEnabled();
        });
        test("should enabled create draft button when draft name is entered - navigated using Drafts Drawer", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await demandPlanPage.enterDraftName(testdata.txtDraftname);
            await expect(demandPlanPage.createDraftButton).toBeEnabled();
        });
        test("should disabled create draft button when draft name is entered and then deleted - navigated using Demand Plan Page", async ({ }) => {
            await demandPlanPage.clickButtonAddDraft();
            await demandPlanPage.enterDraftName(testdata.txtDraftname);
            await demandPlanPage.enterDraftName("");
            await expect(demandPlanPage.createDraftButton).toBeDisabled();
        });
        test("should disabled create draft button when draft name is entered and then deleted - navigated using Drafts Drawer", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await demandPlanPage.enterDraftName(testdata.txtDraftname);
            await demandPlanPage.enterDraftName("");
            await expect(demandPlanPage.createDraftButton).toBeDisabled();
        });
        test("should create draft successfully - navigated using Demand Plan Page", async ({ }) => {
            expectedSuccessMessage = '';
            actualSuccessMessage = '';
            expectedSuccessMessage = 'Draft Saved';
            await demandPlanPage.clickButtonAddDraft();
            actualSuccessMessage = await demandPlanPage.createDraft(
                testdata.txtDraftname,
                testdata.txtDraftDescription
            );
            actualSuccessMessage = actualSuccessMessage.trim();
            console.log('Actual Success Message: ' + actualSuccessMessage);
            console.log('Expected Success Message: ' + expectedSuccessMessage);
            expect(actualSuccessMessage).toBe(expectedSuccessMessage);
        });
        test("should restore draft successfully", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.clickButtonRestoreDraft();
            await expect(demandPlanPage.successMessage).toBeVisible();
        });
        test("should view restored draft successfully", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.clickButtonViewRestoreDraft();
            await expect(demandPlanPage.demandPlanPageLabel).toHaveText(
                testdata.txtDraftname + " close"
            );
        });
        test("should close restored draft successfully", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.clickButtonViewRestoreDraft();
            await expect(demandPlanPage.demandPlanPageLabel).toHaveText(
                testdata.txtDraftname + " close"
            );
            await demandPlanPage.clickButtonCloseRestoredDraft();
            await expect(demandPlanPage.demandPlanPageLabel).toHaveText(
                "Demand Plan"
            );
        });
        test("should delete draft successfully", async ({ }) => {
            await page.waitForTimeout(2000);
            expectedSuccessMessage = '';
            actualSuccessMessage = '';
            expectedSuccessMessage = 'Draft Deleted';
            actualSuccessMessage = await demandPlanPage.deleteSavedDraft(testdata.txtDraftname);
            actualSuccessMessage = actualSuccessMessage.trim();
            console.log('Actual Success Message: ' + actualSuccessMessage);
            console.log('Expected Success Message: ' + expectedSuccessMessage);
            expect(actualSuccessMessage).toBe(expectedSuccessMessage);
        });
        test("should create draft successfully - navigated using Drafts Drawer", async ({ }) => {
            await demandPlanPage.openDraftsDrawer();
            await demandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await demandPlanPage.createDraft(
                testdata.txtDraftname,
                testdata.txtDraftDescription
            );
            await expect(demandPlanPage.successMessage).toBeVisible();
        });
    });
    test.describe.serial("Task Management", () => {
        test("should open Add Task Form successfully", async ({ }) => {
            await demandPlanPage.openAddTaskForm();
            await expect(demandPlanPage.taskSubjectInput).toBeVisible();
            await expect(demandPlanPage.taskMessageInput).toBeVisible();
            await expect(demandPlanPage.assignedToDropdown).toBeVisible();
            await expect(demandPlanPage.dueDateInput).toBeVisible();
            await expect(demandPlanPage.BtnCancelAddTaskForm).toBeVisible();
            await expect(demandPlanPage.createTaskButton).toBeVisible();
        });
        test("should close Add Task Form successfully by clicking close form button", async ({ }) => {
            await demandPlanPage.openAddTaskForm();
            await demandPlanPage.clickButtonCloseWindowAddTaskForm();
            await page.waitForTimeout(4000);
            await expect(demandPlanPage.taskSubjectInput).not.toBeVisible();
            await expect(demandPlanPage.taskMessageInput.first()).not.toBeVisible();
            await expect(demandPlanPage.assignedToDropdown).not.toBeVisible();
            await expect(demandPlanPage.dueDateInput).not.toBeVisible();
            await expect(demandPlanPage.BtnCancelAddTaskForm).not.toBeVisible();
            await expect(demandPlanPage.createTaskButton).not.toBeVisible();
        });
        test("should close Add Task Form successfully by clicking cancel button", async ({ }) => {
            await demandPlanPage.openAddTaskForm();
            await demandPlanPage.clickButtonCancelCreateTaskForm();
            await page.waitForTimeout(4000);
            await expect(demandPlanPage.taskSubjectInput).not.toBeVisible();
            await expect(demandPlanPage.taskMessageInput).not.toBeVisible();
            await expect(demandPlanPage.assignedToDropdown).not.toBeVisible();
            await expect(demandPlanPage.dueDateInput).not.toBeVisible();
            await expect(demandPlanPage.BtnCancelAddTaskForm).not.toBeVisible();
            await expect(demandPlanPage.createTaskButton).not.toBeVisible();
        });
        test("should disabled create task button when task subject is empty", async ({ }) => {
            await demandPlanPage.openAddTaskForm();
            await expect(demandPlanPage.createTaskButton).toBeDisabled();
        });
        test("should enabled create task button when mandatory fields are entered", async ({ }) => {
            await demandPlanPage.openAddTaskForm();
            await demandPlanPage.enterTaskSubject(testdata.txtTaskSubject);
            await demandPlanPage.enterMessage(testdata.txtTaskMessage);
            await demandPlanPage.selectAssignToFromDropdown(
                testdata.chkBoxAssignedTo
            );
            await expect(demandPlanPage.createTaskButton).toBeEnabled();
        });
        test("should disabled create task button when task subject is entered and then deleted", async ({ }) => {
            await demandPlanPage.openAddTaskForm();
            await demandPlanPage.enterTaskSubject(testdata.txtTaskSubject);
            await demandPlanPage.enterTaskSubject("");
            await expect(demandPlanPage.createTaskButton).toBeDisabled();
        });
        test("should create task successfully", async ({ }) => {
            await demandPlanPage.openAddTaskForm();
            await demandPlanPage.createTask(
                testdata.txtTaskSubject,
                testdata.txtTaskMessage,
                testdata.chkBoxAssignedTo,
                testdata.dtDueDate
            );
            await expect(demandPlanPage.successMessage).toBeVisible();
        });
    });
    test.describe.skip("Export Data", () => {
        test("should open export options successfully", async ({ }) => {
            await demandPlanPage.clickButtonExpandExportOptionsList();
            await expect(demandPlanPage.exportToExcelButton).toBeVisible();
        });
        test("should close export options successfully", async ({ }) => {
            await demandPlanPage.clickButtonExpandExportOptionsList();
            await demandPlanPage.clickButtonExpandExportOptionsList();
            await expect(demandPlanPage.exportToExcelButton).not.toBeVisible();
        });
        test("should export data to Excel successfully", async ({ }) => {
            await demandPlanPage.exportDataToExcel(testdata.txtviewname);
            await expect(demandPlanPage.successMessage).toBeVisible();
        });
    });
    //------------------After Each------------------------------------
    test.afterEach(async ({ }, testInfo) => {
        console.log(`========== FINISHING TEST: ${testInfo.title} ==========`);
        if (testInfo.status === 'passed') {
            console.log(`✅ TEST PASSED: ${testInfo.title}`);
        } else if (testInfo.status === 'failed') {
            console.log(`❌ TEST FAILED: ${testInfo.title}`);
        }
        // Clean up test artifacts based on test type
        console.log('Starting test cleanup...');
        try {
            // Clean up drafts if this was a draft-related test
            if (testInfo.title === 'should create draft successfully - navigated using Demand Plan Page'
                || testInfo.title === 'should restore draft successfully'
                || testInfo.title === 'should view restored draft successfully'
                || testInfo.title === 'should close restored draft successfully'
                || testInfo.title === 'should create draft successfully - navigated using Drafts Drawer'
            ) {
                console.log('Cleaning up test drafts...');
                try {
                    await demandPlanPage.openDraftsDrawer();
                    // Check if the draft exists before trying to delete it
                    const draftExists = await page.isVisible(`text="${testdata.txtDraftname}"`, { timeout: 3000 });
                    if (draftExists) {
                        await page.waitForTimeout(5000);
                        expectedSuccessMessage = '';
                        actualSuccessMessage = '';
                        expectedSuccessMessage = 'Draft Deleted';
                        console.log(`Deleting draft: ${testdata.txtDraftname}`);
                        actualSuccessMessage = await demandPlanPage.deleteSavedDraft(testdata.txtDraftname);
                        actualSuccessMessage = actualSuccessMessage.trim();
                        console.log('Actual Success Message: ' + actualSuccessMessage);
                        console.log('Expected Success Message: ' + expectedSuccessMessage);
                        expect(actualSuccessMessage).toBe(expectedSuccessMessage);
                    }
                    await demandPlanPage.closeDraftDrawer();
                } catch (draftError) {
                    console.log(`Error cleaning up draft: ${draftError}`);
                }
            }

        } catch (cleanupError) {
            console.error(`Error during test cleanup: ${cleanupError}`);
        } finally {
            console.log('Reloading page to reset state...');
            try {
                await page.reload();
                console.log('Page reloaded successfully');
            } catch (reloadError) {
                console.error(`Error reloading page: ${reloadError}`);
            }

            console.log(`Test cleanup completed for: ${testInfo.title}`);
            console.log('='.repeat(60));
        }
    });
    //-------------------After All------------------------------------
    test.afterAll(async () => {
        console.log('Starting Demand Plan Page test suite cleanup...');

        try {
            // Navigate to Demand Plan page to ensure we're in the right context
            console.log('Navigating to Demand Plan page for final cleanup...');
            await dashboardPage.clickDemandPlanPageLink();

            // Final cleanup of test data
            console.log('Performing final cleanup of test data...');
            //await performFinalCleanup();

            // Logout to leave the system in a clean state
            console.log('Logging out...');
            await dashboardPage.clickLogout();
            await expect(loginPage.userNameLocator).toBeVisible({ timeout: 10000 });
            console.log('Logged out successfully');

            // Close the browser
            console.log('Closing browser...');
            await page.close();
            console.log('Demand Plan Page test suite cleanup completed successfully');
        } catch (error) {
            console.error('Error during test suite cleanup:', error);
            // Ensure page is closed even if there's an error
            try {
                console.log('Attempting to close browser despite errors...');
                await page.close();
                console.log('Browser closed');
            } catch (closeError) {
                console.error('Failed to close browser:', closeError);
            }
        }
    });
});
