import test from "@playwright/test";
import TaskPage from "../pages/tasksPage";

const subject = "Test Task";
const searchText = "John User";
const message = "No message";
const calendarData = `${new Date().toLocaleString(undefined, {
  month: "long",
})} ${new Date().getDate()},`;

test.describe("Task Page Test", () => {
  test.skip("check correct URL", async ({ page }) => {
    const taskPage = new TaskPage(page);

    await taskPage.navigateToTaskPage();

    await taskPage.verifyPageTitle();

    await taskPage.findLocator();

    await taskPage.createTaskButtonClick();

    await taskPage.fillFormData({
      subject,
      searchText,
      message,
      calendarData,
    });

    await taskPage.verifyNewAddedRow({
      subject,
      searchText,
    });

    await taskPage.navigateToNewAddedRow(subject);

    await taskPage.verifyPageTitle();
  });
});
