import { expect, Locator, <PERSON> } from "@playwright/test";

export default class LoginPage {
    private page: Page;

    public IFUserName: Locator;
    public userNameLocator: Locator;
    public IFPassword: Locator;
    public BtnLogin: Locator;
    public ChkBxRememberMe: Locator;
    public ShowPasswordButton: Locator;
    public ErrorMessage: Locator;

    constructor(page: Page) {
        this.page = page;
        this.initializeElements();
    }

    async initializeElements() {
        this.IFUserName = this.page.locator("//input[@id='username']");
        this.userNameLocator = this.page.locator("//input[@id='username']");
        this.IFPassword = this.page.locator("//input[@id='password']");
        this.BtnLogin = this.page.locator("//input[@id='kc-login']");
        this.ChkBxRememberMe = this.page.locator("//label[normalize-space()='Remember me']");
        this.ShowPasswordButton = this.page.locator('button[aria-label="Show password"]');
        this.ErrorMessage = this.page.locator('.error-message');
    }

    async getPageTitle(): Promise<string> {
        const title = await this.page.title();
        console.log('Actual Page Title - ' + title);
        return title;
    }

    async enterUserName(username: string) {
        console.log('Login Page - Inserting data into Username input field');
        await this.IFUserName.fill(username);
        // await this.IFUserName.fill(username);
    }

    async enterPassword(password: string) {
        console.log('Login Page - Inserting data into Password input field');
        await this.IFPassword.fill(password);
    }

    async checkboxRememberCheckUncheck(checkStatus: string) {
        if (checkStatus === 'check') {
            await this.rememberCheckboxCheck();
        } else if (checkStatus === 'uncheck') {
            await this.rememberCheckboxUncheck();
        }
    }

    async rememberCheckboxCheck() {
        console.log('Login Page - Clicking Remember Me Checkbox to Check');
        await this.ChkBxRememberMe.check();
    }

    async rememberCheckboxUncheck() {
        console.log('Login Page - Clicking Remember Me Checkbox to Uncheck');
        await this.ChkBxRememberMe.uncheck();
    }

    async clickLoginBtn() {
        console.log('Login Page - Clicking on Login button');
        await this.BtnLogin.click();
    }

    async loginProcess(txtusername: string, txtpassword: string) {
        await this.enterUserName(txtusername);
        await this.enterPassword(txtpassword);
        await this.clickLoginBtn();
    }

    async BeforeEachloginProcess() {
        await this.enterUserName('User');
        await this.enterPassword('Password123');
        await this.clickLoginBtn();
    }
    async BeforeEachloginProcessForLocal(txtusername: string, txtpassword: string) {
        await this.enterUserName(txtusername);
        await this.enterPassword(txtpassword);
        await this.clickLoginBtn();
    }
    async getErrorMessage(): Promise<string> {
        await this.ErrorMessage.waitFor({ state: 'visible' });
        return await this.ErrorMessage.textContent() || '';
    }
}
function elseif(arg0: string) {
    throw new Error("Function not implemented.");
}

