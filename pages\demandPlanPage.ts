import { Locator, Page } from "@playwright/test";
export default class DemandPlanPage {
    private page: Page;
    // Navigation Links
    public dashboardPageLink: Locator;
    public demandPlanPageLink: Locator;
    public tasksPageLink: Locator;
    public usersPageLink: Locator;
    public rolesPageLink: Locator;
    public groupsPageLink: Locator;
    public hypercubePageLink: Locator;

    // Header Controls
    public openHypercubeDrawerButton: Locator;
    public toggleDarkModeButton: Locator;
    public openNotificationDrawerButton: Locator;
    public logoutButton: Locator;
    public userProfileButton: Locator;

    // Demand Plan Page Navigation and Control Elements
    public demandPlanPageLabel: Locator;
    public openSavedViewsListButton: Locator;
    public openGraphSectionButton: Locator;
    public openAddViewFormButton: Locator;
    public openSavedViewsSettingFormButton: Locator;
    public expandMoreButton: Locator;
    public expandLessButton: Locator;
    public openDraftsDrawerButton: Locator;

    // Demand Plan Page Control Elements
    public exportButton: Locator;
    public exportToExcelExpandMore: Locator;
    public exportToExcelButton: Locator;
    public openAddTaskFormButton: Locator;
    public openAddDraftFormButtonFromDemandPlanPage: Locator;
    public fullScreenViewButton: Locator;
    public openCopyLinkUrlFormButton: Locator;
    public lockRowButton: Locator;
    public unlockRowButton: Locator;

    // Filter and Attribute Elements
    public attributesDropdown: Locator;
    public generatMeasuresDropdown: Locator;
    public timeMeasuresDropdown: Locator;
    public timePeriodDropdown: Locator;
    public timeHierarchyDropdown: Locator;

    // Breadcrumbs
    public departmentBreadcrumb: Locator;
    public classBreadcrumb: Locator;
    public subClassBreadcrumb: Locator;
    public regionBreadcrumb: Locator;
    public stateBreadcrumb: Locator;
    public storeBreadcrumb: Locator;
    public productBreadcrumb: Locator;

    // Attributes Dropdown
    public searchBoxAttributesDD: Locator;
    public btnSelectAllAttributesDD: Locator;
    public buttonDeselectAllAttributesDropDown: Locator;
    public chkBoxDepartment: Locator;
    public chkBoxClass: Locator;
    public chkBoxSubclass: Locator;
    public chkBoxRegion: Locator;
    public chkBoxState: Locator;
    public chkBoxStore: Locator;
    public chkBoxProduct: Locator;

    // View Management Elements
    public viewNameInput: Locator;
    public viewDescriptionInput: Locator;
    public createViewButton: Locator;
    public globalViewCheckbox: Locator;
    public cancelViewButton: Locator;
    public closeViewFormButton: Locator;
    public deleteSavedViewButton: Locator;
    public confirmDeleteViewButton: Locator;
    public cancelDeleteViewButton: Locator;

    // Drafts Drawer
    public draftsLabel: Locator;
    public draftsDrawer: Locator;
    public closeDraftsDrawerButton: Locator;
    public addDraftButtonFromDraftsDrawer: Locator;
    public deleteDraftButton: Locator;
    public restoreDraftButton: Locator;
    public viewRestoreDraftButton: Locator;
    public closeRestoredDraftButton: Locator;

    // Draft Management Elements(Save Draft From)
    public addDraftlabel: Locator;
    public closeAddDraftsFormButton: Locator;
    public draftNamelabel: Locator;
    public draftNameInput: Locator;
    public draftDescriptionlabel: Locator;
    public draftDescriptionInput: Locator;
    public cancelAddDraftFormButton: Locator;
    public createDraftButton: Locator;

    // Task Management Elements
    public taskSubjectInput: Locator;
    public taskMessageInput: Locator;
    public assignedToDropdown: Locator;
    public DDAssignedToSearchBox: Locator;
    public dueDateInput: Locator;
    public BtnCancelAddTaskForm: Locator;
    public createTaskButton: Locator;
    public BtnCloseFormAddtaskForm: Locator;

    // Saved Views Setting Form Elements
    public savedViewsSettingLabel: Locator;
    public closeSavedViewsSettingFormButton: Locator;
    public savedViewsSettingForm: Locator;
    public cancelSavedViewsSettingButton: Locator;
    public saveSavedViewsSettingButton: Locator;
    public savedViewsSettingFormText: Locator;

    // Helper Methods
    public successMessage: Locator;

    constructor(page: Page) {
        this.page = page;
        this.initializeElements();
    }

    async initializeElements() {

        // Navigation Links
        this.dashboardPageLink = this.page.locator("//mat-icon[normalize-space()='dashboard']");
        this.demandPlanPageLink = this.page.locator("//mat-icon[@class='mat-icon notranslate mat-mdc-tooltip-trigger material-icons-outlined mat-icon-no-color'][normalize-space()='insert_chart']");
        this.tasksPageLink = this.page.locator("//mat-icon[@class='mat-icon notranslate mat-mdc-tooltip-trigger material-icons-outlined mat-icon-no-color'][normalize-space()='fact_check']");
        this.usersPageLink = this.page.locator("//mat-icon[normalize-space()='account_circle']");
        this.rolesPageLink = this.page.locator("//mat-icon[normalize-space()='verified_user']");
        this.groupsPageLink = this.page.locator("//mat-icon[normalize-space()='people']");
        this.hypercubePageLink = this.page.locator("//mat-icon[@class='mat-icon notranslate mat-mdc-tooltip-trigger material-icons-outlined mat-icon-no-color'][normalize-space()='emoji_objects']");

        // Header Controls
        this.openHypercubeDrawerButton = this.page.locator("//mat-icon[@class='mat-icon notranslate material-icons-outlined mat-accent'][normalize-space()='emoji_objects']");
        this.toggleDarkModeButton = this.page.locator("//mat-icon[normalize-space()='dark_mode']");
        this.openNotificationDrawerButton = this.page.locator("//mat-icon[@class='mat-icon notranslate mat-badge material-icons-outlined mat-icon-no-color mat-badge-overlap mat-badge-above mat-badge-after mat-badge-medium ng-star-inserted']");
        this.logoutButton = this.page.locator("//mat-icon[normalize-space()='power_settings_new']");
        this.userProfileButton = this.page.getByRole('link', { name: 'JU' });

        // Demand Plan Page Navigation and Control Elements
        this.demandPlanPageLabel = this.page.getByTestId('appTitle');
        this.openSavedViewsListButton = this.page.locator("//span[normalize-space()='Saved Views']");
        this.openGraphSectionButton = this.page.locator("//span[normalize-space()='Graph']");
        this.openAddViewFormButton = this.page.locator("//span[normalize-space()='Add View']");
        this.openSavedViewsSettingFormButton = this.page.locator("//mat-icon[normalize-space()='settings']");
        this.expandMoreButton = this.page.locator("//mat-icon[@class='mat-icon notranslate ng-tns-c3614833739-14 material-icons-outlined mat-icon-no-color'][normalize-space()='expand_more']");
        this.expandLessButton = this.page.locator("//mat-icon[normalize-space()='expand_less']");
        this.openDraftsDrawerButton = this.page.getByText('design_servicesDrafts');

        // Demand Plan Page Control Elements
        this.exportToExcelExpandMore = this.page.locator("//mat-icon[@class='mat-icon notranslate material-symbols-outlined material-icons-outlined mat-icon-no-color'][normalize-space()='expand_more']");
        this.exportToExcelButton = this.page.locator("//button[@role='menuitem']");
        this.openAddTaskFormButton = this.page.locator("//mat-icon[normalize-space()='add_task']");
        this.openAddDraftFormButtonFromDemandPlanPage = this.page.locator("//mat-icon[@class='mat-icon notranslate material-icons-outlined mat-icon-no-color'][normalize-space()='design_services']");
        this.fullScreenViewButton = this.page.locator("//mat-icon[normalize-space()='expand_content']");
        this.openCopyLinkUrlFormButton = this.page.locator("//mat-icon[normalize-space()='link']");
        this.lockRowButton = this.page.locator("//mat-icon[normalize-space()='lock']");
        this.unlockRowButton = this.page.locator("//mat-icon[normalize-space()='lock_reset']");

        // Filter Elements
        this.attributesDropdown = this.page.locator('div').filter({ hasText: /^Attributes 3$/ });
        this.generatMeasuresDropdown = this.page.locator("//span[@class='mat-mdc-select-placeholder mat-mdc-select-min-line ng-tns-c1711764913-23 ng-star-inserted']");
        this.timeMeasuresDropdown = this.page.locator("//mat-select-trigger[@class='d-flex flex-row justify-content-between me-1 ng-tns-c1711764913-24 ng-star-inserted']");
        this.timePeriodDropdown = this.page.locator("//div[@class='cdk-overlay-backdrop cdk-overlay-transparent-backdrop cdk-overlay-backdrop-showing']");
        this.timeHierarchyDropdown = this.page.locator("//mat-select-trigger[@class='d-flex flex-row justify-content-between me-1 ng-tns-c1711764913-27 ng-star-inserted']");

        // Breadcrumbs Elements
        this.departmentBreadcrumb = this.page.locator("//span[@class='mat-mdc-select-value-text ng-tns-c1711764913-29 ng-star-inserted']");
        this.classBreadcrumb = this.page.locator("//mat-select-trigger[@class='ng-tns-c1711764913-31 ng-star-inserted']");
        this.subClassBreadcrumb = this.page.locator("//mat-select-trigger[@class='ng-tns-c1711764913-33 ng-star-inserted']");
        this.regionBreadcrumb = this.page.locator("//mat-select-trigger[@class='ng-tns-c1711764913-82 ng-star-inserted']");
        this.stateBreadcrumb = this.page.locator("//div[@id='mat-select-value-93']");
        this.storeBreadcrumb = this.page.locator("//div[@id='mat-select-value-95']");
        this.productBreadcrumb = this.page.locator("//div[@class='mat-mdc-select-arrow ng-tns-c1711764913-88']//*[name()='svg']");

        // Attributes Dropdown Elements
        this.searchBoxAttributesDD = this.page.locator("//input[@placeholder='Search']");
        this.btnSelectAllAttributesDD = this.page.locator("//div[@class='select-all']");
        this.buttonDeselectAllAttributesDropDown = this.page.locator("//div[@class='deselect-all']");
        this.chkBoxDepartment = this.page.getByRole('option', { name: 'Department' }).locator('mat-pseudo-checkbox');
        this.chkBoxClass = this.page.getByRole('option', { name: 'Class' }).locator('mat-pseudo-checkbox');
        this.chkBoxSubclass = this.page.getByRole('option', { name: 'Subclass' }).locator('mat-pseudo-checkbox');
        this.chkBoxRegion = this.page.getByRole('option', { name: 'Region' }).locator('mat-pseudo-checkbox');
        this.chkBoxState = this.page.getByRole('option', { name: 'State' }).locator('mat-pseudo-checkbox');
        this.chkBoxStore = this.page.getByRole('option', { name: 'Store' }).locator('mat-pseudo-checkbox');
        this.chkBoxProduct = this.page.getByRole('option', { name: 'Product' }).locator('mat-pseudo-checkbox');

        // View Management Elements
        this.viewNameInput = this.page.getByRole('textbox', { name: 'Enter View name' });
        this.viewDescriptionInput = this.page.getByRole('textbox', { name: 'Enter Description' });
        this.createViewButton = this.page.locator("//span[normalize-space()='Create']");
        this.globalViewCheckbox = this.page.getByRole('checkbox', { name: 'Global' });
        this.cancelViewButton = this.page.locator("//button[@class='mdc-button mdc-button--outlined mat-mdc-outlined-button mat-accent mat-mdc-button-base cdk-focused cdk-mouse-focused']//span[@class='mat-mdc-button-touch-target']");
        this.closeViewFormButton = this.page.locator("//mat-icon[normalize-space()='close']");
        this.deleteSavedViewButton = this.page.locator("//mat-icon[normalize-space()='delete']");
        this.confirmDeleteViewButton = this.page.locator("//span[normalize-space()='Delete']");
        this.cancelDeleteViewButton = this.page.locator("//span[normalize-space()='Cancel']");

        // Drafts Drawer Elements
        this.draftsLabel = this.page.getByRole('heading', { name: 'Drafts' });
        this.draftsDrawer = this.page.locator('div').filter({ hasText: 'add Add Draft' }).nth(1);
        this.closeDraftsDrawerButton = this.page.getByText('close', { exact: true });
        this.addDraftButtonFromDraftsDrawer = this.page.getByRole('button', { name: 'add draft', exact: true });
        this.deleteDraftButton = this.page.locator('a').filter({ hasText: 'delete' });
        this.restoreDraftButton = this.page.locator('a').filter({ hasText: 'save' });
        this.viewRestoreDraftButton = this.page.locator('a').filter({ hasText: 'tab_group' });
        this.closeRestoredDraftButton = this.page.getByTestId('appTitle').getByRole('button', { name: 'close' });

        // Draft Management Elements(Save Draft From)
        this.addDraftlabel = this.page.getByLabel('Add Draftclose').getByText('Add Draft');
        this.closeAddDraftsFormButton = this.page.getByRole('button').filter({ hasText: 'close' });
        this.draftNamelabel = this.page.getByText('Draft Name');
        this.draftNameInput = this.page.getByRole('textbox', { name: 'Enter Draft name' });
        this.draftDescriptionlabel = this.page.getByText('Description', { exact: true });
        this.draftDescriptionInput = this.page.getByRole('textbox', { name: 'Enter Description' });
        this.cancelAddDraftFormButton = this.page.getByRole('button', { name: 'Cancel' });
        this.createDraftButton = this.page.locator("//span[normalize-space()='Create']");

        // Task Management Elements
        this.taskSubjectInput = this.page.getByRole('textbox', { name: 'Add Subject' });
        this.taskMessageInput = this.page.getByRole('paragraph');
        this.assignedToDropdown = this.page.getByText('Assigned ToAdd Assigned To');
        this.DDAssignedToSearchBox = this.page.getByPlaceholder('Search');
        this.dueDateInput = this.page.locator("//input[@name='dueDate']");
        this.BtnCancelAddTaskForm = this.page.locator("//span[normalize-space()='Cancel']");
        this.createTaskButton = this.page.locator("//span[contains(text(),'Create')]");
        this.BtnCloseFormAddtaskForm = this.page.locator("//mat-icon[normalize-space()='clear']");

        // Saved Views Setting Form Elements
        this.savedViewsSettingLabel = this.page.getByRole('textbox', { name: 'Add Subject' });
        this.closeSavedViewsSettingFormButton = this.page.getByRole('textbox', { name: 'Add Subject' });
        this.savedViewsSettingForm = this.page.getByRole('textbox', { name: 'Add Subject' });
        this.cancelSavedViewsSettingButton = this.page.getByRole('textbox', { name: 'Add Subject' });
        this.saveSavedViewsSettingButton = this.page.getByRole('textbox', { name: 'Add Subject' });
        this.savedViewsSettingFormText = this.page.getByRole('textbox', { name: 'Add Subject' });

        // Helper Methods
        this.successMessage = this.page.locator("//div[@class='mat-mdc-snack-bar-label mdc-snackbar__label']");
    }


    // Navigation Methods
    async navigateTo(link: Locator, description: string) {
        console.log(`Dashboard Page - Clicking ${description}`);
        await link.click();
    }
    async clickDashboardPageLink() {
        await this.navigateTo(this.dashboardPageLink, "dashboard page link");
    }
    async clickDemandPlanPageLink() {
        await this.navigateTo(this.demandPlanPageLink, "demand plan page link");
    }
    async clickTasksPageLink() {
        await this.navigateTo(this.tasksPageLink, "tasks page link");
    }
    async clickUsersPageLink() {
        await this.navigateTo(this.usersPageLink, "users page link");
    }
    async clickRolesPageLink() {
        await this.navigateTo(this.rolesPageLink, "roles page link");
    }
    async clickGroupsPageLink() {
        await this.navigateTo(this.groupsPageLink, "groups page link");
    }
    async clickHypercubePageLink() {
        await this.navigateTo(this.hypercubePageLink, "hypercube page link");
    }
    // Header Control Methods
    async clickHypercube() {
        await this.navigateTo(this.openHypercubeDrawerButton, "hypercube button");
    }
    async toggleDarkMode() {
        await this.navigateTo(this.toggleDarkModeButton, "dark/light mode button");
    }
    async clickNotification() {
        await this.navigateTo(this.openNotificationDrawerButton, "notification button");
    }
    async clickLogout() {
        await this.navigateTo(this.logoutButton, "logout button");
    }
    async clickUserProfile() {
        await this.navigateTo(this.userProfileButton, "User Profile button");
    }

    // Demand Plan Page Navigation and Control Elements
    async getPageTitle(): Promise<string> {
        const title = await this.page.title();
        console.log('Actual Page Title - ' + title);
        return title;
    }
    async openSavedViewsList() {
        console.log('Demand Plan Page - Click Saved Views button');
        await this.openSavedViewsListButton.click();
        await this.page.waitForLoadState('networkidle');
    }
    async openGraphSection() {
        console.log('Demand Plan Page - Click Graph button');
        await this.openGraphSectionButton.click();
        await this.page.waitForLoadState('networkidle');
    }
    async openAddViewForm() {
        console.log('Demand Plan Page - Click Add View button');
        await this.openAddViewFormButton.click();
        await this.page.waitForLoadState('networkidle');
    }
    async openSavedViewsSettingForm() {
        console.log('Demand Plan Page - Click Saved Views Setting button');
        await this.openSavedViewsSettingFormButton.click();
        await this.page.waitForLoadState('networkidle');
    }
    async expandPageDrawer() {
        console.log('Demand Plan Page - Click Expand More button');
        await this.expandMoreButton.click();
        await this.page.waitForLoadState('networkidle');
    }
    async expandLessPageDrawer() {
        console.log('Demand Plan Page - Click Expand Less button');
        await this.expandLessButton.click();
        await this.page.waitForLoadState('networkidle');
    }
    async openDraftsDrawer() {
        console.log('Demand Plan Page - Click Drafts button');
        await this.openDraftsDrawerButton.click();
        await this.page.waitForLoadState('networkidle');
    }

    // Demand Plan Page Control Elements
    async clickButtonExpandExportOptionsList() {
        console.log('Demand Plan Page - Clicking Expand Export Option List Button');
        await this.expandMoreButton.click();
    }
    async clickButtonExportToExcel() {
        console.log('Demand Plan Page - Clicking Export To Excel Button');
        await this.exportToExcelButton.click();
    }
    async openAddTaskForm() {
        console.log('Demand Plan Page - Clicking Add Task Button');
        await this.openAddTaskFormButton.click();
    }
    async openAddDraftFormFromDemandPlanPage() {
        console.log('Demand Plan Page - Clicking Add Draft Button');
        await this.openAddDraftFormButtonFromDemandPlanPage.click();
    }
    async fullScreenView() {
        console.log('Demand Plan Page - Clicking Full Screen View Button');
        await this.fullScreenViewButton.click();
    }
    async openCopyLinkUrlForm() {
        console.log('Demand Plan Page - Clicking Copy Link Url Button');
        await this.openCopyLinkUrlFormButton.click();
    }
    async lockRow() {
        console.log('Demand Plan Page - Clicking Lock Row Button');
        await this.lockRowButton.click();
    }
    async unlockRow() {
        console.log('Demand Plan Page - Clicking Unlock Row Button');
        await this.unlockRowButton.click();
    }

    // Filter Elements
    async openAttributesDropdown() {
        console.log('Demand Plan Page - Clicking Attributes Dropdown');
        await this.attributesDropdown.click();
    }
    async openGeneralMeasuresDropdown() {
        console.log('Demand Plan Page - Clicking General Measures Dropdown');
        await this.generatMeasuresDropdown.click();
    }
    async openTimeMeasuresDropdown() {
        console.log('Demand Plan Page - Clicking Time Measures Dropdown');
        await this.timeMeasuresDropdown.click();
    }
    async openTimePeriodDropdown() {
        console.log('Demand Plan Page - Clicking Time Period Dropdown');
        await this.timePeriodDropdown.click();
    }
    async openTimeHierarchyDropdown() {
        console.log('Demand Plan Page - Clicking Time Hierarchy Dropdown');
        await this.timeHierarchyDropdown.click();
    }

    // Breadcrumbs Elements
    async openDepartmentBreadcrumb() {
        console.log('Demand Plan Page - Clicking Department Breadcrumb');
        await this.departmentBreadcrumb.click();
    }
    async openClassBreadcrumb() {
        console.log('Demand Plan Page - Clicking Class Breadcrumb');
        await this.classBreadcrumb.click();
    }
    async openSubClassBreadcrumb() {
        console.log('Demand Plan Page - Clicking Subclass Breadcrumb');
        await this.subClassBreadcrumb.click();
    }
    async openRegionBreadcrumb() {
        console.log('Demand Plan Page - Clicking Region Breadcrumb');
        await this.regionBreadcrumb.click();
    }
    async openStateBreadcrumb() {
        console.log('Demand Plan Page - Clicking State Breadcrumb');
        await this.stateBreadcrumb.click();
    }
    async openStoreBreadcrumb() {
        console.log('Demand Plan Page - Clicking Store Breadcrumb');
        await this.storeBreadcrumb.click();
    }
    async openProductBreadcrumb() {
        console.log('Demand Plan Page - Clicking Product Breadcrumb');
        await this.productBreadcrumb.click();
    }

    // Attributes Dropdown Elements
    async clickSearchBoxAttributesDropdown() {
        console.log('Demand Plan Page - Clicking Search Box in Attributes Dropdown');
        await this.searchBoxAttributesDD.click();
    }
    async searchAttributesDropdown(searchText: string) {
        console.log(`Demand Plan Page - Typing ${searchText} in Search Box in Attributes Dropdown`);
        await this.searchBoxAttributesDD.fill(searchText);
    }
    async selectAllAttributesDropdownElements() {
        console.log('Demand Plan Page - Clicking Select All button in Attributes Dropdown');
        await this.btnSelectAllAttributesDD.click();
    }
    async deselectAllAttributesDropdownElements() {
        console.log('Demand Plan Page - Clicking Deselect All button in Attributes Dropdown');
        await this.buttonDeselectAllAttributesDropDown.click();
    }
    async selectDepartmentCheckbox() {
        console.log('Demand Plan Page - Clicking Department Checkbox in Attributes Dropdown');
        await this.chkBoxDepartment.click();
    }
    async selectClassCheckbox() {
        console.log('Demand Plan Page - Clicking Class Checkbox in Attributes Dropdown');
        await this.chkBoxClass.click();
    }
    async selectSubclassCheckbox() {
        console.log('Demand Plan Page - Clicking Subclass Checkbox in Attributes Dropdown');
        await this.chkBoxSubclass.click();
    }
    async selectRegionCheckbox() {
        console.log('Demand Plan Page - Clicking Region Checkbox in Attributes Dropdown');
        await this.chkBoxRegion.click();
    }
    async selectStateCheckbox() {
        console.log('Demand Plan Page - Clicking State Checkbox in Attributes Dropdown');
        await this.chkBoxState.click();
    }
    async selectStoreCheckbox() {
        console.log('Demand Plan Page - Clicking Store Checkbox in Attributes Dropdown');
        await this.chkBoxStore.click();
    }
    async selectProductCheckbox() {
        console.log('Demand Plan Page - Clicking Product Checkbox in Attributes Dropdown');
        await this.chkBoxProduct.click();
    }

    // View Management Actions and Methods
    async enterViewName(viewname: string) {
        console.log('Create View Page - Inserting data into Viewname input field');
        await this.viewNameInput.fill(viewname);
        await this.page.waitForLoadState('networkidle');
    }
    async enterDescription(description: string) {
        console.log('Create View Page - Inserting data into Description input field');
        await this.viewDescriptionInput.fill(description);
    }
    async clickButtonCancel() {
        console.log('Create View Page - Clicking Cancel Button');
        await this.cancelViewButton.click();
    }
    async clickButtonCloseWindowAddViewForm() {
        console.log('Create View Page - Clicking Close Window Button');
        await this.closeViewFormButton.click();
    }
    async clickButtonCreateView() {
        console.log('Create View Page - Clicking Create View Button');
        await this.createViewButton.click();
    }
    async clickCheckboxGlobalToCheckUnckeck(isGlobal: string) {
        if (isGlobal === "Yes") {
            if (await this.globalViewCheckbox.isChecked()) {
            } else {
                await this.globalViewCheckbox.click();
                console.log('Create View Page - Clicking Global Checkbox Button to check');
            }
        }
        if (isGlobal === "No") {
            if (await this.globalViewCheckbox.isChecked()) {
                await this.globalViewCheckbox.click();
                console.log('Create View Page - Clicking Global Checkbox Button to Uncheck');
            } else {

            }
        }
    }
    async createView(txtviewname: string, txtdescription: string, isGlobal: string): Promise<string> {
        console.log(`Creating ${isGlobal ? 'global' : 'local'} view: ${txtviewname}`);
        await this.enterViewName(txtviewname);
        await this.enterDescription(txtdescription);
        await this.clickCheckboxGlobalToCheckUnckeck(isGlobal);
        await this.createViewButton.click();
        const Message = await this.waitForSuccess();
        return Message;
    }
    async deleteSavedView(): Promise<string> {
        console.log('Demand Plan Page - Deleting view');
        await this.deleteSavedViewButton.click();
        await this.confirmDeleteViewButton.click();
        const Message = await this.waitForSuccess();
        return Message;
    }

    // Drafts Drawer Actions and Methods
    async closeDraftDrawer() {
        console.log('Demand Plan Page - Clicking Close Drafts Drawer Button');
        await this.closeDraftsDrawerButton.click();
    }
    async clickButtonAddDraftFromDraftsDrawer() {
        console.log('Demand Plan Page - Clicking Add Draft button');
        await this.addDraftButtonFromDraftsDrawer.click();
    }
    async clickButtonDeleteDraft() {
        console.log('Demand Plan Page - Clicking Delete Draft button');
        await this.deleteDraftButton.click();
    }
    async clickButtonRestoreDraft() {
        console.log('Demand Plan Page - Clicking Restore Draft button');
        await this.restoreDraftButton.click();
    }
    async clickButtonViewRestoreDraft() {
        console.log('Demand Plan Page - Clicking View Restore Draft button');
        await this.viewRestoreDraftButton.click();
    }
    async clickButtonCloseRestoredDraft() {
        console.log('Demand Plan Page - Clicking Close Restored Draft button');
        await this.closeRestoredDraftButton.click();
    }
    async deleteSavedDraft(draftname: string): Promise<string> {
        console.log('Demand Plan Page - Deleting draft');
        await this.openDraftsDrawerButton.click();
        await this.clickButtonDeleteDraft();
        const Message = await this.waitForSuccess();
        return Message;
    }
    // Draft Management Actions and Methods(Save Draft From)
    async clickButtonAddDraft() {
        console.log('Demand Plan Page - Clicking Add Draft button');
        await this.openAddDraftFormButtonFromDemandPlanPage.click();
    }
    async enterDraftName(draftname: string) {
        console.log('Create Draft Page - Entering draft name:', draftname);
        await this.draftNameInput.waitFor({ state: 'visible' });
        await this.draftNameInput.fill(draftname);
    }
    async enterDescriptionCreateDraftForm(descriptionCreateDraftForm: string) {
        console.log('Create Draft Page - Entering draft description:', descriptionCreateDraftForm);
        await this.draftDescriptionInput.waitFor({ state: 'visible' });
        await this.draftDescriptionInput.fill(descriptionCreateDraftForm);
    }
    async clickButtonCancelAddDraftForm() {
        console.log('Create Draft Page - Clicking Cancel Button');
        await this.cancelAddDraftFormButton.click();
    }
    async clickButtonCloseWindowAddDraftForm() {
        console.log('Create Draft Page - Clicking Close Add Draft Form Button');
        await this.closeAddDraftsFormButton.click();
    }
    async clickButtonCreateDraft() {
        console.log('Create Draft Page - Submitting draft form');
        await this.createDraftButton.click();
    }
    async createDraft(txtdraftname: string, txtdraftdescription: string): Promise<string> {
        console.log('Creating new draft');
        try {
            await this.enterDraftName(txtdraftname);
            await this.enterDescriptionCreateDraftForm(txtdraftdescription);
            await this.clickButtonCreateDraft();
            const Message = await this.waitForSuccess();
            return Message;
        } catch (error) {
            console.error('Error creating draft:', error);
            throw error;
        }
    }


    // Task Management Adctions And Methods
    async enterTaskSubject(subjectname: string) {
        console.log('Create Task Page - Entering task subject:', subjectname);
        await this.taskSubjectInput.fill(subjectname);
    }
    async enterMessage(message: string) {
        console.log('Entering task message:', message);
        await this.taskMessageInput.fill(message);
    }
    async selectAssignToFromDropdown(assignee: string) {
        console.log('Create Task Page - Selecting assignee:', assignee);
        await this.assignedToDropdown.click();
        await this.page.locator(`text=${assignee}`).click();
        await this.page.waitForTimeout(500);
    }
    async selectDueDateFromCalendar(dueDate: string) {
        console.log('Create Task Page - Setting due date:', dueDate);
        await this.dueDateInput.fill(dueDate);
    }
    async clickButtonCancelCreateTaskForm() {
        console.log('Create task Page - Clicking Cancel Button');
        await this.BtnCancelAddTaskForm.click();
    }
    async clickButtonCloseWindowAddTaskForm() {
        console.log('Create task Page - Clicking Close Window Button');
        await this.BtnCloseFormAddtaskForm.click();
    }
    async clickButtonCreateTask() {
        console.log('Create task Page - Clicking Create Task Button');
        await this.createTaskButton.click();
    }

    async createTask(txtsubjectname: string, txtmessage: string, txtassignedTo: string, txtDueDate: string): Promise<string> {
        console.log('Create task Page - Creating new task');
        try {
            await this.enterTaskSubject(txtsubjectname);
            await this.enterMessage(txtmessage);
            await this.selectAssignToFromDropdown(txtassignedTo);
            await this.selectDueDateFromCalendar(txtDueDate);
            await this.clickButtonCreateTask();
            const Message = await this.waitForSuccess();
            return Message;
        } catch (error) {
            console.error('Error exporting data:', error);
            throw error;
        }
    }

    //Export Data Actions and Methods
    async exportDataToExcel(excelFileName: string): Promise<string> {
        console.log('Exporting data to Excel');
        try {
            await this.clickButtonExpandExportOptionsList();
            await this.clickButtonExportToExcel();
            const Message = await this.waitForSuccess();
            return Message;
        } catch (error) {
            console.error('Error exporting data:', error);
            throw error;
        }
    }
    // Helper Methods
    private async waitForSuccess(): Promise<string> {
        await this.successMessage.waitFor({ state: 'visible' });
        const message = await this.successMessage.textContent() || '';
        console.log(`Success message: "${message}"`);
        await this.closeSuccessMessage();
        return message;
    }
    private async closeSuccessMessage() {
        await this.successMessage.waitFor({ state: 'visible', timeout: 1000 });
        await this.successMessage.click();
    }
}

