import { Locator, <PERSON> } from "@playwright/test";

export default class DashboardPage {
  private page: Page;
  //---------------- Navigation Links------------------------
  public dashboardPageLink: Locator;
  public demandPlanPageLink: Locator;
   public pricingDemandPlanPageLink: Locator;
  public tasksPageLink: Locator;
  public usersPageLink: Locator;
  public rolesPageLink: Locator;
  public groupsPageLink: Locator;
  public hypercubePageLink: Locator;
  public editDefaultPageLink: Locator;
  public customizeDashboardPageLink: Locator;
  //---------------- Header Controls------------------------
  public logoutButton: Locator;
  public hypercubeButton: Locator;
  public darkLightModeButton: Locator;
  public notificationButton: Locator;
  public userProfileButton: Locator;
  //------------- Dashboard Controls-----------------------
  public productsDropdown: Locator;
  public productsDropdownList: Locator;
  public locationsDropdown: Locator;
  public locationsDropdownList: Locator;
  public accuracyDropdown1: Locator;
  public accuracyDropdown2: Locator;
  public accuracyDropdown3: Locator;
  public pastDueTab: Locator;
  public openTab: Locator;

  constructor(page: Page) {
    this.page = page;
    this.initializeElements();
  }

  async initializeElements() {
    this.logoutButton = this.page.locator(
      "//mat-icon[normalize-space()='power_settings_new']"
    );
    this.hypercubeButton = this.page.getByRole("button", { name: "Hypercube" });
    this.darkLightModeButton = this.page.locator(
      "//mat-icon[normalize-space()='dark_mode']"
    );
    this.notificationButton = this.page.getByRole("button", {
      name: "notification",
    });
    this.userProfileButton = this.page.getByRole("link", { name: "JU" });
    this.dashboardPageLink = this.page.locator(
      "//mat-icon[normalize-space()='dashboard']"
    );
    this.demandPlanPageLink = this.page.locator(
      "//mat-icon[@class='mat-icon notranslate mat-mdc-tooltip-trigger material-icons-outlined mat-icon-no-color'][normalize-space()='insert_chart']"
    );
    this.pricingDemandPlanPageLink = this.page.getByRole('link').filter({ hasText: 'sell' });
    this.tasksPageLink = this.page.locator(
      "//mat-icon[@class='mat-icon notranslate mat-mdc-tooltip-trigger material-icons-outlined mat-icon-no-color'][normalize-space()='fact_check']"
    );
    this.usersPageLink = this.page.locator(
      "//mat-icon[normalize-space()='account_circle']"
    );
    this.rolesPageLink = this.page.locator(
      "//mat-icon[normalize-space()='verified_user']"
    );
    this.groupsPageLink = this.page.locator(
      "//mat-icon[normalize-space()='people']"
    );
    this.hypercubePageLink = this.page.locator(
      "//mat-icon[@class='mat-icon notranslate mat-mdc-tooltip-trigger material-icons-outlined mat-icon-no-color'][normalize-space()='emoji_objects']"
    );
    this.editDefaultPageLink = this.page.locator(
      "//span[normalize-space()='Edit Default']"
    );
    this.customizeDashboardPageLink = this.page.locator(
      "//span[normalize-space()='Customize Dashboard']"
    );
    this.productsDropdown = this.page.getByRole("combobox", {
      name: "Products",
    });
    this.productsDropdownList = this.page.locator(
      "//mat-icon[normalize-space()='chevron_right']"
    );
    this.locationsDropdown = this.page.getByRole("combobox", {
      name: "Locations",
    });
    this.locationsDropdownList = this.page.locator(
      "//mat-icon[normalize-space()='chevron_right']"
    );
    this.accuracyDropdown1 = this.page.locator(
      "//div[@id='mat-select-value-1']"
    );
    this.accuracyDropdown2 = this.page.locator(
      "//div[@id='mat-select-value-3']"
    );
    this.accuracyDropdown3 = this.page.locator(
      "//div[@id='mat-select-value-5']"
    );
    this.pastDueTab = this.page.locator(
      "//span[contains(text(),'Past Due (7)')]"
    );
    this.openTab = this.page.locator(
      "//span[@class='mdc-tab__text-label'][normalize-space()='Open']"
    );
  }
  // Navigation Methods
  async navigateTo(link: Locator, description: string) {
    console.log(`Dashboard Page - Clicking ${description}`);
    await link.click();
  }
  async clickDashboardPageLink() {
    await this.navigateTo(this.dashboardPageLink, "dashboard page link");
  }
  async clickDemandPlanPageLink() {
    await this.navigateTo(this.demandPlanPageLink, "demand plan page link");
  }
   async clickPricingDemandPlanPageLink() {
    await this.navigateTo(this.pricingDemandPlanPageLink, "pricing demand plan page link");
  }
  async clickTasksPageLink() {
    await this.navigateTo(this.tasksPageLink, "tasks page link");
  }
  async clickUsersPageLink() {
    await this.usersPageLink.waitFor({ state: "visible", timeout: 15000 });
    await this.navigateTo(this.usersPageLink, "users page link");
  }
  async clickRolesPageLink() {
    await this.navigateTo(this.rolesPageLink, "roles page link");
  }
  async clickGroupsPageLink() {
    await this.navigateTo(this.groupsPageLink, "groups page link");
  }
  async clickHypercubePageLink() {
    await this.navigateTo(this.hypercubePageLink, "hypercube page link");
  }
  async clickEditDefaultDashboardPageLink() {
    await this.navigateTo(this.editDefaultPageLink, "edit default page link");
  }
  async clickCustomizeDashboardPageLink() {
    await this.navigateTo(
      this.customizeDashboardPageLink,
      "customize dashboard page link"
    );
  }
  // Header Control Methods
  async clickLogout() {
    await this.navigateTo(this.logoutButton, "logout button");
  }
  async clickNotification() {
    await this.navigateTo(this.notificationButton, "notification button");
  }
  async toggleDarkMode() {
    await this.navigateTo(this.darkLightModeButton, "dark/light mode button");
  }
  async clickHypercube() {
    await this.navigateTo(this.hypercubeButton, "hypercube button");
  }
  // Dashboard Control Methods
  async clickProductsDropdown() {
    await this.navigateTo(this.productsDropdown, "products dropdown");
  }
  async clickLocationsDropdown() {
    await this.navigateTo(this.locationsDropdown, "locations dropdown");
  }
  async clickAccuracyDropdown(dropdownNumber: 1 | 2 | 3) {
    const dropdown = this[`accuracyDropdown${dropdownNumber}`];
    await this.navigateTo(dropdown, `accuracy dropdown ${dropdownNumber}`);
  }
  async clickPastDueTab() {
    await this.navigateTo(this.pastDueTab, "past due tab");
  }
  async clickOpenTab() {
    await this.navigateTo(this.openTab, "open tab");
  }
  // Visibility Check Methods
  async isElementVisible(locator: Locator): Promise<boolean> {
    await locator.waitFor({ state: "visible" });
    return await locator.isVisible();
  }
  async checkAllNavigationLinksVisible(): Promise<boolean> {
    const links = [
      this.dashboardPageLink,
      this.demandPlanPageLink,
      this.pricingDemandPlanPageLink,
      this.tasksPageLink,
      this.usersPageLink,
      this.rolesPageLink,
      this.groupsPageLink,
      this.hypercubePageLink,
      this.editDefaultPageLink,
      this.customizeDashboardPageLink,
    ];
    for (const link of links) {
      if (!(await this.isElementVisible(link))) {
        return false;
      }
    }
    return true;
  }
  async getPageTitle(expectedTitle: string): Promise<string> {
    console.log("Expected Page Title is - " + expectedTitle);
    const actualTitle = await this.page.title();
    console.log("Actual Page Title is - " + actualTitle);
    return actualTitle;
  }
}
