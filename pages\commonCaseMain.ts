import { expect, Locator, Page } from "@playwright/test";

export class CommonCase {
  private page: Page;
  private grid: Locator;

  constructor(page: Page) {
    this.page = page;
  }

  async verifyPageTitle(text: string) {
    await expect(this.page.getByTestId("appTitle")).toHaveText(text);
  }

  async identifyAGRoot() {
    this.grid = await this.page.locator(".ag-root");
  }

  async verifyGridHeader(headerNames: string[]) {
    await this.identifyAGRoot();

    await expect(this.grid).toBeVisible();

    headerNames.forEach(
      async (h, i) =>
        await expect(
          this.grid.locator(".ag-header-cell-text").nth(i)
        ).toHaveText(h)
    );
  }

  async verifyPgination(pageSize: string) {
    await this.identifyAGRoot();

    await expect(this.grid).toBeVisible();

    await expect(this.page.locator(".ag-picker-field-display")).toHaveText(
      pageSize
    );

    const nextButton = await this.page.getByRole("button", {
      name: "Next Page",
    });

    const previousButton = await this.page.getByRole("button", {
      name: "Previous Page",
    });

    let pageCount = 1;
    if (await nextButton.isEnabled()) {
      while (await nextButton.isEnabled()) {
        expect(nextButton).toBeEnabled();
        if (pageCount === 1) {
          expect(previousButton).toBeDisabled();
        } else {
          expect(previousButton).toBeEnabled();
        }

        nextButton.click();

        pageCount += 1;

        await expect(
          this.page
            .locator("span")
            .filter({ hasText: `Page ${pageCount} of` })
            .first()
        ).toBeVisible();
      }
    }

    if (await previousButton.isEnabled()) {
      while (await previousButton.isEnabled()) {
        expect(nextButton).toBeDisabled();

        if (pageCount === 1) {
          expect(previousButton).toBeDisabled();
          expect(nextButton).toBeEnabled();
        } else {
          expect(previousButton).toBeEnabled();
        }

        previousButton.click();

        pageCount -= 1;

        await expect(
          this.page
            .locator("span")
            .filter({ hasText: `Page ${pageCount} of` })
            .first()
        ).toBeVisible();
      }
    }
  }
}
