import { expect, Locator, <PERSON> } from '@playwright/test';

export class CommonCase {
  private page: Page;
  private grid: Locator;
  private toast: Locator;
  private loader: Locator;
  private dialog: Locator;
  private errorMessage: Locator;

 

  constructor(page: Page) {
    this.page = page;
    this.initializeLocators();
  }

  private initializeLocators() {
    this.grid = this.page.locator('.ag-root');
    this.toast = this.page.locator('.toast-message');
    this.loader = this.page.locator('.loading-spinner');
    this.dialog = this.page.locator('.modal-dialog');
    this.errorMessage = this.page.locator('.error-message');
  }
  // Page Title Verification
  async verifyPageTitle(text: string) {
    await expect(this.page.getByTestId('appTitle')).toHaveText(text);
    console.log(`Verified page title: ${text}`);
  }

  // Grid Operations
  async verifyGridHeader(headerNames: string[]) {
    await expect(this.grid).toBeVisible();
    
    for (let i = 0; i < headerNames.length; i++) {
      await expect(
        this.grid.locator('.ag-header-cell-text').nth(i)
      ).toHaveText(headerNames[i]);
    }
    console.log('Verified grid headers:', headerNames);
  }

  /*async verifyGridHeader(headerNames: string[]) {
   this.grid = await this.page.locator('.ag-root');

    await expect(this.grid).toBeVisible();

    headerNames.forEach(
      async (h, i) => await expect(this.grid.locator('.ag-header-cell-text').nth(i)).toHaveText(h),
    );
  }
}
*/
async verifyGridData(expectedData: string[][]) {
    await expect(this.grid).toBeVisible();
    
    for (let row = 0; row < expectedData.length; row++) {
      for (let col = 0; col < expectedData[row].length; col++) {
        const cellLocator = this.grid.locator(`.ag-row[row-index="${row}"] .ag-cell[col-id="${col}"]`);
        await expect(cellLocator).toHaveText(expectedData[row][col]);
      }
    }
    console.log('Verified grid data matches expected values');
  }

  // Loading State Management
  async waitForLoaderToDisappear() {
    try {
      await this.loader.waitFor({ state: 'hidden', timeout: 10000 });
      console.log('Loader disappeared successfully');
    } catch (error) {
      console.error('Loader did not disappear within timeout:', error);
      throw error;
    }
  }

  // Toast Message Handling
  async verifyToastMessage(expectedMessage: string) {
    await expect(this.toast).toBeVisible();
    await expect(this.toast).toHaveText(expectedMessage);
    console.log(`Verified toast message: ${expectedMessage}`);
  }

  async waitForToastToDisappear() {
    await this.toast.waitFor({ state: 'hidden' });
    console.log('Toast message disappeared');
  }

  // Dialog Operations
  async verifyDialogContent(expectedTitle: string, expectedMessage?: string) {
    await expect(this.dialog).toBeVisible();
    await expect(this.dialog.locator('.modal-title')).toHaveText(expectedTitle);
    
    if (expectedMessage) {
      await expect(this.dialog.locator('.modal-body')).toHaveText(expectedMessage);
    }
    console.log(`Verified dialog content - Title: ${expectedTitle}`);
  }

  async closeDialog() {
    await this.dialog.locator('.close-button').click();
    await expect(this.dialog).toBeHidden();
    console.log('Closed dialog');
  }

  // Form Operations
  async fillFormField(fieldLocator: Locator, value: string) {
    await fieldLocator.fill(value);
    await expect(fieldLocator).toHaveValue(value);
    console.log(`Filled form field with value: ${value}`);
  }

  async selectDropdownOption(dropdownLocator: Locator, optionText: string) {
    await dropdownLocator.click();
    await this.page.getByRole('option', { name: optionText }).click();
    console.log(`Selected dropdown option: ${optionText}`);
  }

  // Error Handling
  async verifyErrorMessage(expectedError: string) {
    await expect(this.errorMessage).toBeVisible();
    await expect(this.errorMessage).toHaveText(expectedError);
    console.log(`Verified error message: ${expectedError}`);
  }

  // Navigation Helpers
  async navigateAndVerify(url: string, expectedTitle: string) {
    await this.page.goto(url);
    await this.verifyPageTitle(expectedTitle);
    console.log(`Navigated to ${url} and verified title`);
  }

  // File Operations
  async uploadFile(fileInputLocator: Locator, filePath: string) {
    await fileInputLocator.setInputFiles(filePath);
    console.log(`Uploaded file: ${filePath}`);
  }

  async downloadFile(downloadTriggerLocator: Locator): Promise<string> {
    const downloadPromise = this.page.waitForEvent('download');
    await downloadTriggerLocator.click();
    const download = await downloadPromise;
    console.log(`Downloaded file: ${download.suggestedFilename()}`);
    return download.path();
  }

  // Date Picker Operations
  async selectDate(datePickerLocator: Locator, date: Date) {
    await datePickerLocator.click();
    await this.page.getByRole('button', { name: date.toLocaleDateString() }).click();
    console.log(`Selected date: ${date.toLocaleDateString()}`);
  }

  // Checkbox Operations
  async toggleCheckbox(checkboxLocator: Locator, shouldBeChecked: boolean) {
    const isChecked = await checkboxLocator.isChecked();
    if (isChecked !== shouldBeChecked) {
      await checkboxLocator.click();
      console.log(`Toggled checkbox to ${shouldBeChecked ? 'checked' : 'unchecked'}`);
    }
  }

  // Table Operations
  async verifyTableCell(row: number, column: number, expectedValue: string) {
    const cell = this.page.locator(`table tr:nth-child(${row + 1}) td:nth-child(${column + 1})`);
    await expect(cell).toHaveText(expectedValue);
    console.log(`Verified table cell [${row},${column}] has value: ${expectedValue}`);
  }

  // Utility Methods
  async takeScreenshot(name: string) {
    await this.page.screenshot({ path: `./screenshots/${name}.png` });
    console.log(`Took screenshot: ${name}.png`);
  }

  async waitForNetworkIdle() {
    await this.page.waitForLoadState('networkidle');
    console.log('Network activity settled');
  }

  async retryOperation(operation: () => Promise<void>, maxAttempts = 1) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        await operation();
        return;
      } catch (error) {
        if (attempt === maxAttempts) throw error;
        console.log(`Attempt ${attempt} failed, retrying...`);
        await this.page.waitForTimeout(1000 * attempt);
      }
    }
  }
}