import { Browser, expect, Page, test } from "@playwright/test";
import { webkit, chromium, firefox } from "@playwright/test";

import DashboardPage from "../pages/dashboardPage";
import LoginPage from "../pages/loginPage";
import PricingDemandPlanPage from "../pages/pricingDemandPlanPage";
const testdata = JSON.parse(JSON.stringify(require("..//testData.json")));

let page: Page;
let dashboardPage;
let loginPage;
let pricingDemandPlanPage;
let expectedSuccessMessage: string;
let actualSuccessMessage: string;
let expectedPageTitle: string;
let txtviewname: string;
let txtviewdescription: string;
let ExpectedSuccessMessageDisplayedStatus: boolean;
let isChkboxChecked: string;
let txtTaskSubject: string;
let txtTaskMessage: string;
let chkBoxAssignedTo: string;
let dtDueDate: string;
let txtDraftname: string;
let txtDraftDescription: string;
let currentCheckedStatus: boolean;

test.describe('Pricing Demand Plan Page Tests', () => {
      //-------------------Before All------------------------------------
    test.beforeAll(async ({ baseURL }) => {
        console.log("Starting test suite setup for Pricing Demand Plan Page Tests");
        try {
            const browser: Browser = await chromium.launch({ headless: false });
            page = await browser.newPage();
            // Create a new page and navigate to the base URL
            console.log(`Navigating to base URL: ${baseURL}`);
            await page.goto(`${baseURL}`);
            // Initialize page objects
            console.log('Initializing page objects...');
            dashboardPage = new DashboardPage(page);
            loginPage = new LoginPage(page);
            pricingDemandPlanPage = new PricingDemandPlanPage(page);
            //Initialize page elements
            await loginPage.initializeElements();
            await dashboardPage.initializeElements();
            await pricingDemandPlanPage.initializeElements();
            //Perform login
            console.log('Performing login...');
            await loginPage.BeforeEachloginProcess();
            console.log('Login completed');
            await page.waitForLoadState('networkidle');
            //Verify successful login
            console.log('Verifying successful login...');
            await expect(dashboardPage.logoutButton).toBeVisible();
            console.log('Login verification completed');
            console.log('Pricing Demand Plan Page test suite setup completed');
        } catch (error) {
            console.error('Error in Pricing Demand Plan Page beforeAll setup:', error);
        }
    });
    test.beforeEach(async ({ }, testInfo) => {
        console.log(`========== STARTING TEST: ${testInfo.title} ==========`);
        // Reset all test variables to initial state
        expectedPageTitle = "";
        expectedSuccessMessage = "";
        actualSuccessMessage = "";
        txtviewname = "";
        txtviewdescription = "";
        ExpectedSuccessMessageDisplayedStatus = false;
        isChkboxChecked = "";
        txtTaskSubject = "";
        txtTaskMessage = "";
        chkBoxAssignedTo = "";
        dtDueDate = "";
        txtDraftname = "";
        txtDraftDescription = "";
        currentCheckedStatus = false;
        // Navigate to Pricing Demand Plan page and ensure it's fully loaded
        console.log('Navigating to Pricing Demand Plan page...');
        await dashboardPage.clickPricingDemandPlanPageLink();
        await page.waitForLoadState('networkidle');
        await expect(pricingDemandPlanPage.pricingDemandPlanPageLabel).toBeVisible();
        // Set up test-specific prerequisites based on test title
        console.log(`Setting up prerequisites for test: ${testInfo.title}`);
        try {
            // Draft-related test setup
            if (testInfo.title === 'should delete draft successfully'
                || testInfo.title === 'should restore draft successfully'
                || testInfo.title === 'should view restored draft successfully'
                || testInfo.title === 'should close restored draft successfully') {
                console.log(`Creating draft for test: ${testInfo.title}`);
                expectedSuccessMessage = '';
                expectedSuccessMessage = 'Draft Saved';
                actualSuccessMessage = '';
                await pricingDemandPlanPage.clickButtonAddDraft();
                actualSuccessMessage = await pricingDemandPlanPage.createDraft(testdata.txtDraftname, testdata.txtDraftDescription);
                actualSuccessMessage = actualSuccessMessage.trim();
                console.log('Actual Success Message: ' + actualSuccessMessage);
                console.log('Expected Success Message: ' + expectedSuccessMessage);
                expect(actualSuccessMessage).toBe(expectedSuccessMessage);
                // Wait for success message to disappear
                await page.waitForTimeout(1000);
                // Verify draft was created
                await pricingDemandPlanPage.openDraftsDrawer();
                const draftExists = await page.isVisible(`text="${testdata.txtDraftname}"`, { timeout: 5000 });
                expect(draftExists).toBeTruthy();
                await pricingDemandPlanPage.closeDraftDrawer();
            }
        } catch (setupError) {
            console.error(`Error during test setup for ${testInfo.title}:`, setupError);
            // Try to recover by reloading the page
            console.log('Attempting to recover from setup failure by reloading page...');
            await page.reload();
            await page.waitForLoadState('networkidle');
        }
        console.log(`Test setup completed for: ${testInfo.title}`);
    });

    test.describe.serial("Navigation and Basic UI Elements", () => {
        test("should have correct page label text", async () => {
            const pageLabelText = "Pricing Demand Plan";
            await expect(pricingDemandPlanPage.pricingDemandPlanPageLabel).toHaveText(
                pageLabelText
            );
        });
        test("should have correct page title", async () => {
            const expectedTitle = "Demand Intelligence";
            const actualTitle = await dashboardPage.getPageTitle(expectedTitle);
            await expect(actualTitle).toBe(expectedTitle);
        });
        test("should have correct page URL", async () => {
            await expect(page).toHaveURL(/.*pricing-demand-plan/);
        });
        test("should have all required UI elements visible", async () => {
            await expect(pricingDemandPlanPage.dashboardPageLink).toBeVisible();
            await expect(pricingDemandPlanPage.demandPlanPageLink).toBeVisible();
             await expect(pricingDemandPlanPage.pricingDemandPlanPageLink).toBeVisible();
            await expect(pricingDemandPlanPage.tasksPageLink).toBeVisible();
            await expect(pricingDemandPlanPage.usersPageLink).toBeVisible();
            await expect(pricingDemandPlanPage.rolesPageLink).toBeVisible();
            await expect(pricingDemandPlanPage.groupsPageLink).toBeVisible();
            await expect(pricingDemandPlanPage.hypercubePageLink).toBeVisible();
            await expect(pricingDemandPlanPage.openHypercubeDrawerButton).toBeVisible();
            await expect(pricingDemandPlanPage.toggleDarkModeButton).toBeVisible();
            await expect(pricingDemandPlanPage.openNotificationDrawerButton).toBeVisible();
            await expect(pricingDemandPlanPage.logoutButton).toBeVisible();
            await expect(pricingDemandPlanPage.userProfileButton).toBeVisible();
            await expect(pricingDemandPlanPage.pricingDemandPlanPageLabel).toBeVisible();
            await expect(pricingDemandPlanPage.openSavedViewsListButton).toBeVisible();
            await expect(pricingDemandPlanPage.openGraphSectionButton).toBeVisible();
            await expect(pricingDemandPlanPage.openAddViewFormButton).toBeVisible();
            await expect(pricingDemandPlanPage.openSavedViewsSettingFormButton).toBeVisible();
            await expect(pricingDemandPlanPage.expandLessButton).toBeVisible();
            await expect(pricingDemandPlanPage.openDraftsDrawerButton).toBeVisible();
            await expect(pricingDemandPlanPage.exportToExcelExpandMore).toBeVisible();
            await expect(pricingDemandPlanPage.openAddTaskFormButton).toBeVisible();
            await expect(pricingDemandPlanPage.openAddDraftFormButtonFromDemandPlanPage).toBeVisible();
            await expect(pricingDemandPlanPage.fullScreenViewButton).toBeVisible();
            await expect(pricingDemandPlanPage.openCopyLinkUrlFormButton).toBeVisible();
            await expect(pricingDemandPlanPage.lockRowButton).toBeVisible();
        });
    });
    test.describe.serial("View Management", () => {
        test("should open Add View Form successfully", async ({ }) => {
            await pricingDemandPlanPage.openAddViewForm();
            await expect(pricingDemandPlanPage.viewNameInput).toBeVisible();
            await expect(pricingDemandPlanPage.viewDescriptionInput).toBeVisible();
            await expect(pricingDemandPlanPage.createViewButton).toBeVisible();
            await expect(pricingDemandPlanPage.globalViewCheckbox).toBeVisible();
        });
        test("should close Add View Form successfully by clicking close form button", async ({ }) => {
            await pricingDemandPlanPage.openAddViewForm();
            await pricingDemandPlanPage.clickButtonCloseWindowAddViewForm();
            await expect(pricingDemandPlanPage.viewNameInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.viewDescriptionInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.createViewButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.globalViewCheckbox).not.toBeVisible();
        });
        test("should close Add View Form successfully by clicking cancel button", async ({ }) => {
            await pricingDemandPlanPage.openAddViewForm();
            await pricingDemandPlanPage.clickButtonCancelAddDraftForm();
            await expect(pricingDemandPlanPage.viewNameInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.viewDescriptionInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.createViewButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.globalViewCheckbox).not.toBeVisible();
        });
        test("should disabled create view button when view name is empty", async ({ }) => {
            await pricingDemandPlanPage.openAddViewForm();
            await expect(pricingDemandPlanPage.createViewButton).toBeDisabled();
        });
        test("should enabled create view button when view name is entered", async ({ }) => {
            await pricingDemandPlanPage.openAddViewForm();
            await pricingDemandPlanPage.enterViewName(testdata.txtviewnamelocal);
            await expect(pricingDemandPlanPage.createViewButton).toBeEnabled();
        });
        test("should disabled create view button when view name is entered and then deleted", async ({ }) => {
            await pricingDemandPlanPage.openAddViewForm();
            await pricingDemandPlanPage.enterViewName(testdata.txtviewnamelocal);
            await pricingDemandPlanPage.enterViewName("");
            await expect(pricingDemandPlanPage.createViewButton).toBeDisabled();
        });
        test("should create and delete local view successfully", async ({ }) => {
            expectedSuccessMessage = 'View Saved';
            await pricingDemandPlanPage.openAddViewForm();
            actualSuccessMessage = await pricingDemandPlanPage.createView(
                testdata.txtviewnamelocal,
                testdata.txtviewdescription,
                testdata.isChkboxCheckedLocal
            );
            actualSuccessMessage = actualSuccessMessage.trim();
            console.log('Actual Success Message: ' + actualSuccessMessage);
            console.log('Expected Success Message: ' + expectedSuccessMessage);
            expect(actualSuccessMessage).toBe(expectedSuccessMessage);
            await page.waitForTimeout(4000);
            expectedSuccessMessage = '';
            expectedSuccessMessage = 'View Deleted';
            actualSuccessMessage = '';
            actualSuccessMessage = await pricingDemandPlanPage.deleteSavedView();
            actualSuccessMessage = actualSuccessMessage.trim();
            console.log('Actual Success Message: ' + actualSuccessMessage);
            console.log('Expected Success Message: ' + expectedSuccessMessage);
            expect(actualSuccessMessage).toBe(expectedSuccessMessage);
        });
        test("should create and delete global view successfully", async ({ }) => {
            await pricingDemandPlanPage.openAddViewForm();
            await pricingDemandPlanPage.createView(
                testdata.txtviewnameglobal,
                testdata.txtviewdescription,
                testdata.isChkboxCheckedGlobal
            );
            await expect(pricingDemandPlanPage.successMessage).toBeVisible();
            await page.waitForTimeout(4000);
            await pricingDemandPlanPage.deleteSavedView();
            await expect(pricingDemandPlanPage.successMessage).toBeVisible();
        });
    });
    test.describe.serial("Draft Management", () => {
        test("should open Drafts Drawer successfully", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await expect(pricingDemandPlanPage.addDraftButtonFromDraftsDrawer).toBeVisible();
            await expect(pricingDemandPlanPage.closeDraftsDrawerButton).toBeVisible();
            await expect(pricingDemandPlanPage.draftsDrawer).toBeVisible();
            await expect(pricingDemandPlanPage.draftsLabel).toBeVisible();
        });
        test("should close Drafts Drawer successfully", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.closeDraftDrawer();
            await expect(
                pricingDemandPlanPage.addDraftButtonFromDraftsDrawer
            ).not.toBeVisible();
            await expect(pricingDemandPlanPage.closeDraftsDrawerButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftsDrawer).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftsLabel).not.toBeVisible();
        });
        test("should open save Draft Form successfully from Drafts Drawer", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await expect(pricingDemandPlanPage.addDraftlabel).toBeVisible();
            await expect(pricingDemandPlanPage.closeAddDraftsFormButton).toBeVisible();
            await expect(pricingDemandPlanPage.draftNamelabel).toBeVisible();
            await expect(pricingDemandPlanPage.draftNameInput).toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionlabel).toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionInput).toBeVisible();
            await expect(pricingDemandPlanPage.cancelAddDraftFormButton).toBeVisible();
            await expect(pricingDemandPlanPage.createDraftButton).toBeVisible();
        });
        test("should open save Draft Form successfully from Demand Plan Page", async ({ }) => {
            await pricingDemandPlanPage.clickButtonAddDraft();
            await expect(pricingDemandPlanPage.addDraftlabel).toBeVisible();
            await expect(pricingDemandPlanPage.closeAddDraftsFormButton).toBeVisible();
            await expect(pricingDemandPlanPage.draftNamelabel).toBeVisible();
            await expect(pricingDemandPlanPage.draftNameInput).toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionlabel).toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionInput).toBeVisible();
            await expect(pricingDemandPlanPage.cancelAddDraftFormButton).toBeVisible();
            await expect(pricingDemandPlanPage.createDraftButton).toBeVisible();
        });
        test("should close save Draft Form successfully by clicking close form button, navigated using Demand Plan Page", async ({ }) => {
            await pricingDemandPlanPage.clickButtonAddDraft();
            await pricingDemandPlanPage.clickButtonCloseWindowAddDraftForm();
            await expect(pricingDemandPlanPage.addDraftlabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.closeAddDraftsFormButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftNamelabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftNameInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionlabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.cancelAddDraftFormButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.createDraftButton).not.toBeVisible();
        });
        test("should close save Draft Form successfully from Demand Plan Page by clicking close form button, navigated using Drafts Drawer", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await pricingDemandPlanPage.clickButtonCloseWindowAddDraftForm();
            await expect(pricingDemandPlanPage.addDraftlabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.closeAddDraftsFormButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftNamelabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftNameInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionlabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.cancelAddDraftFormButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.createDraftButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.addDraftButtonFromDraftsDrawer).toBeVisible();
            await expect(pricingDemandPlanPage.closeDraftsDrawerButton).toBeVisible();
            await expect(pricingDemandPlanPage.draftsDrawer).toBeVisible();
            await expect(pricingDemandPlanPage.draftsLabel).toBeVisible();
        });
        test("should close save Draft Form successfully by clicking cancel button,navigated using Demand Plan Page", async ({ }) => {
            await pricingDemandPlanPage.clickButtonAddDraft();
            await pricingDemandPlanPage.clickButtonCancelAddDraftForm();
            await expect(pricingDemandPlanPage.addDraftlabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.closeAddDraftsFormButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftNamelabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftNameInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionlabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.cancelAddDraftFormButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.createDraftButton).not.toBeVisible();
        });
        test("should close save Draft Form successfully from Demand Plan Page by clicking cancel button, navigated using Drafts Drawer", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await pricingDemandPlanPage.clickButtonCancelAddDraftForm();
            await expect(pricingDemandPlanPage.addDraftlabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.closeAddDraftsFormButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftNamelabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftNameInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionlabel).not.toBeVisible();
            await expect(pricingDemandPlanPage.draftDescriptionInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.cancelAddDraftFormButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.createDraftButton).not.toBeVisible();
            await expect(pricingDemandPlanPage.addDraftButtonFromDraftsDrawer).toBeVisible();
            await expect(pricingDemandPlanPage.closeDraftsDrawerButton).toBeVisible();
            await expect(pricingDemandPlanPage.draftsDrawer).toBeVisible();
            await expect(pricingDemandPlanPage.draftsLabel).toBeVisible();
        });
        test("should disabled create draft button when draft name is empty - navigated using Demand Plan Page", async ({ }) => {
            await pricingDemandPlanPage.clickButtonAddDraft();
            await expect(pricingDemandPlanPage.createDraftButton).toBeDisabled();
        });
        test("should disabled create draft button when draft name is empty - navigated using Drafts Drawer", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await expect(pricingDemandPlanPage.createDraftButton).toBeDisabled();
        });
        test("should enabled create draft button when draft name is entered - navigated using Demand Plan Page", async ({ }) => {
            await pricingDemandPlanPage.clickButtonAddDraft();
            await pricingDemandPlanPage.enterDraftName(testdata.txtDraftname);
            await expect(pricingDemandPlanPage.createDraftButton).toBeEnabled();
        });
        test("should enabled create draft button when draft name is entered - navigated using Drafts Drawer", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await pricingDemandPlanPage.enterDraftName(testdata.txtDraftname);
            await expect(pricingDemandPlanPage.createDraftButton).toBeEnabled();
        });
        test("should disabled create draft button when draft name is entered and then deleted - navigated using Demand Plan Page", async ({ }) => {
            await pricingDemandPlanPage.clickButtonAddDraft();
            await pricingDemandPlanPage.enterDraftName(testdata.txtDraftname);
            await pricingDemandPlanPage.enterDraftName("");
            await expect(pricingDemandPlanPage.createDraftButton).toBeDisabled();
        });
        test("should disabled create draft button when draft name is entered and then deleted - navigated using Drafts Drawer", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await pricingDemandPlanPage.enterDraftName(testdata.txtDraftname);
            await pricingDemandPlanPage.enterDraftName("");
            await expect(pricingDemandPlanPage.createDraftButton).toBeDisabled();
        });
        test("should create draft successfully - navigated using Demand Plan Page", async ({ }) => {
            expectedSuccessMessage = '';
            actualSuccessMessage = '';
            expectedSuccessMessage = 'Draft Saved';
            await pricingDemandPlanPage.clickButtonAddDraft();
            actualSuccessMessage = await pricingDemandPlanPage.createDraft(
                testdata.txtDraftname,
                testdata.txtDraftDescription
            );
            actualSuccessMessage = actualSuccessMessage.trim();
            console.log('Actual Success Message: ' + actualSuccessMessage);
            console.log('Expected Success Message: ' + expectedSuccessMessage);
            expect(actualSuccessMessage).toBe(expectedSuccessMessage);
        });
        test("should restore draft successfully", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.clickButtonRestoreDraft();
            await expect(pricingDemandPlanPage.successMessage).toBeVisible();
        });
        test("should view restored draft successfully", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.clickButtonViewRestoreDraft();
            await expect(pricingDemandPlanPage.pricingDemandPlanPageLabel).toHaveText(
                testdata.txtDraftname + " close"
            );
        });
        test("should close restored draft successfully", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.clickButtonViewRestoreDraft();
            await expect(pricingDemandPlanPage.pricingDemandPlanPageLabel).toHaveText(
                testdata.txtDraftname + " close"
            );
            await pricingDemandPlanPage.clickButtonCloseRestoredDraft();
            await expect(pricingDemandPlanPage.pricingDemandPlanPageLabel).toHaveText(
                "Pricing Demand Plan"
            );
        });
        test("should delete draft successfully", async ({ }) => {
            await page.waitForTimeout(2000);
            expectedSuccessMessage = '';
            actualSuccessMessage = '';
            expectedSuccessMessage = 'Draft Deleted';
            actualSuccessMessage = await pricingDemandPlanPage.deleteSavedDraft(testdata.txtDraftname);
            actualSuccessMessage = actualSuccessMessage.trim();
            console.log('Actual Success Message: ' + actualSuccessMessage);
            console.log('Expected Success Message: ' + expectedSuccessMessage);
            expect(actualSuccessMessage).toBe(expectedSuccessMessage);
        });
        test("should create draft successfully - navigated using Drafts Drawer", async ({ }) => {
            await pricingDemandPlanPage.openDraftsDrawer();
            await pricingDemandPlanPage.clickButtonAddDraftFromDraftsDrawer();
            await pricingDemandPlanPage.createDraft(
                testdata.txtDraftname,
                testdata.txtDraftDescription
            );
            await expect(pricingDemandPlanPage.successMessage).toBeVisible();
        });
    });
    test.describe.serial("Task Management", () => {
        test("should open Add Task Form successfully", async ({ }) => {
            await pricingDemandPlanPage.openAddTaskForm();
            await expect(pricingDemandPlanPage.taskSubjectInput).toBeVisible();
            await expect(pricingDemandPlanPage.taskMessageInput).toBeVisible();
            await expect(pricingDemandPlanPage.assignedToDropdown).toBeVisible();
            await expect(pricingDemandPlanPage.dueDateInput).toBeVisible();
            await expect(pricingDemandPlanPage.BtnCancelAddTaskForm).toBeVisible();
            await expect(pricingDemandPlanPage.createTaskButton).toBeVisible();
        });
        test("should close Add Task Form successfully by clicking close form button", async ({ }) => {
            await pricingDemandPlanPage.openAddTaskForm();
            await pricingDemandPlanPage.clickButtonCloseWindowAddTaskForm();
            await page.waitForTimeout(4000);
            await expect(pricingDemandPlanPage.taskSubjectInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.taskMessageInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.assignedToDropdown).not.toBeVisible();
            await expect(pricingDemandPlanPage.dueDateInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.BtnCancelAddTaskForm).not.toBeVisible();
            await expect(pricingDemandPlanPage.createTaskButton).not.toBeVisible();
        });
        test("should close Add Task Form successfully by clicking cancel button", async ({ }) => {
            await pricingDemandPlanPage.openAddTaskForm();
            await pricingDemandPlanPage.clickButtonCancelCreateTaskForm();
            await page.waitForTimeout(4000);
            await expect(pricingDemandPlanPage.taskSubjectInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.taskMessageInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.assignedToDropdown).not.toBeVisible();
            await expect(pricingDemandPlanPage.dueDateInput).not.toBeVisible();
            await expect(pricingDemandPlanPage.BtnCancelAddTaskForm).not.toBeVisible();
            await expect(pricingDemandPlanPage.createTaskButton).not.toBeVisible();
        });
        test("should disabled create task button when task subject is empty", async ({ }) => {
            await pricingDemandPlanPage.openAddTaskForm();
            await expect(pricingDemandPlanPage.createTaskButton).toBeDisabled();
        });
        test("should enabled create task button when mandatory fields are entered", async ({ }) => {
            await pricingDemandPlanPage.openAddTaskForm();
            await pricingDemandPlanPage.enterTaskSubject(testdata.txtTaskSubject);
            await pricingDemandPlanPage.enterMessage(testdata.txtTaskMessage);
            await pricingDemandPlanPage.selectAssignToFromDropdown(
                testdata.chkBoxAssignedTo
            );
            await expect(pricingDemandPlanPage.createTaskButton).toBeEnabled();
        });
        test("should disabled create task button when task subject is entered and then deleted", async ({ }) => {
            await pricingDemandPlanPage.openAddTaskForm();
            await pricingDemandPlanPage.enterTaskSubject(testdata.txtTaskSubject);
            await pricingDemandPlanPage.enterTaskSubject("");
            await expect(pricingDemandPlanPage.createTaskButton).toBeDisabled();
        });
        test("should create task successfully", async ({ }) => {
            await pricingDemandPlanPage.openAddTaskForm();
            await pricingDemandPlanPage.createTask(
                testdata.txtTaskSubject,
                testdata.txtTaskMessage,
                testdata.chkBoxAssignedTo,
                testdata.dtDueDate
            );
            await expect(pricingDemandPlanPage.successMessage).toBeVisible();
        });
    });
    test.describe.skip("Export Data", () => {
        test("should open export options successfully", async ({ }) => {
            await pricingDemandPlanPage.clickButtonExpandExportOptionsList();
            await expect(pricingDemandPlanPage.exportToExcelButton).toBeVisible();
        });
        test("should close export options successfully", async ({ }) => {
            await pricingDemandPlanPage.clickButtonExpandExportOptionsList();
            await pricingDemandPlanPage.clickButtonExpandExportOptionsList();
            await expect(pricingDemandPlanPage.exportToExcelButton).not.toBeVisible();
        });
        test("should export data to Excel successfully", async ({ }) => {
            await pricingDemandPlanPage.exportDataToExcel(testdata.txtviewname);
            await expect(pricingDemandPlanPage.successMessage).toBeVisible();
        });
    });
    //------------------After Each------------------------------------
    test.afterEach(async ({ }, testInfo) => {
        console.log(`========== FINISHING TEST: ${testInfo.title} ==========`);
        if (testInfo.status === 'passed') {
            console.log(`✅ TEST PASSED: ${testInfo.title}`);
        } else if (testInfo.status === 'failed') {
            console.log(`❌ TEST FAILED: ${testInfo.title}`);
        }
        // Clean up test artifacts based on test type
        console.log('Starting test cleanup...');
        try {
            // Clean up drafts if this was a draft-related test
            if (testInfo.title === 'should create draft successfully - navigated using Demand Plan Page'
                || testInfo.title === 'should restore draft successfully'
                || testInfo.title === 'should view restored draft successfully'
                || testInfo.title === 'should close restored draft successfully'
            || testInfo.title === 'should create draft successfully - navigated using Drafts Drawer'
            ) {
                console.log('Cleaning up test drafts...');
                try {
                    await pricingDemandPlanPage.openDraftsDrawer();
                    // Check if the draft exists before trying to delete it
                    const draftExists = await page.isVisible(`text="${testdata.txtDraftname}"`, { timeout: 3000 });
                    if (draftExists) {
                        await page.waitForTimeout(5000);
                        expectedSuccessMessage = '';
                        actualSuccessMessage = '';
                        expectedSuccessMessage = 'Draft Deleted';
                        console.log(`Deleting draft: ${testdata.txtDraftname}`);
                        actualSuccessMessage = await pricingDemandPlanPage.deleteSavedDraft(testdata.txtDraftname);
                        actualSuccessMessage = actualSuccessMessage.trim();
                        console.log('Actual Success Message: ' + actualSuccessMessage);
                        console.log('Expected Success Message: ' + expectedSuccessMessage);
                        expect(actualSuccessMessage).toBe(expectedSuccessMessage);
                    }
                    await pricingDemandPlanPage.closeDraftDrawer();
                } catch (draftError) {
                    console.log(`Error cleaning up draft: ${draftError}`);
                }
            }

        } catch (cleanupError) {
            console.error(`Error during test cleanup: ${cleanupError}`);
        } finally {
            console.log('Reloading page to reset state...');
            try {
                await page.reload();
                await page.waitForLoadState('networkidle');
                console.log('Page reloaded successfully');
            } catch (reloadError) {
                console.error(`Error reloading page: ${reloadError}`);
            }

            console.log(`Test cleanup completed for: ${testInfo.title}`);
            console.log('='.repeat(60));
        }
    });
    //-------------------After All------------------------------------
    test.afterAll(async () => {
        console.log('Starting Demand Plan Page test suite cleanup...');

        try {
            // Navigate to Demand Plan page to ensure we're in the right context
            console.log('Navigating to Demand Plan page for final cleanup...');
            await dashboardPage.clickPricingDemandPlanPageLink();
            await page.waitForLoadState('networkidle');

            // Final cleanup of test data
            console.log('Performing final cleanup of test data...');
            //await performFinalCleanup();

            // Logout to leave the system in a clean state
            console.log('Logging out...');
            await dashboardPage.clickLogout();
            await page.waitForLoadState('networkidle');
            await expect(loginPage.IFUserName).toBeVisible({ timeout: 10000 });
            console.log('Logged out successfully');

            // Close the browser
            console.log('Closing browser...');
            await page.close();
            console.log('Demand Plan Page test suite cleanup completed successfully');
        } catch (error) {
            console.error('Error during test suite cleanup:', error);
            // Ensure page is closed even if there's an error
            try {
                console.log('Attempting to close browser despite errors...');
                await page.close();
                console.log('Browser closed');
            } catch (closeError) {
                console.error('Failed to close browser:', closeError);
            }
        }
    });
});
