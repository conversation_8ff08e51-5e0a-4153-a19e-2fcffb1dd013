import { Browser, expect, Page, test } from "@playwright/test";
import { webkit, chromium, firefox } from "@playwright/test";

import LoginPage from "../pages/loginPage";
import DashboardPage from "../pages/dashboardPage";
import DemandPlanPage from "../pages/demandPlanPage";
import TaskPage from "../pages/tasksPage";
import UsersPage from "../pages/usersPage";
import RolesPage from "../pages/rolesPage";
import GroupsPage from "../pages/groupsPage";
import HypercubePage from "../pages/hypercubePage";
import DefaltDashboardCustomization from "../pages/defaultDashboardCustomizationPage";
import DashboardCustomizationPage from "../pages/dashboardCustomizationPage";
const testdata = JSON.parse(JSON.stringify(require("..//testData.json")));


let page: Page;
let loginPage: LoginPage;
let dashboardPage: DashboardPage;
let demandPlanPage: DemandPlanPage;
let tasksPage: TaskPage;
let usersPage: UsersPage;
let rolesPage: RolesPage;
let groupsPage: GroupsPage;
let hypercubePage: HypercubePage;
let defaltDashboardCustomization: DefaltDashboardCustomization;
let dashboardCustomizationPage: DashboardCustomizationPage;
let expectedTitle: string;
let actualTitle: string;

test.describe("Dashboard Page Tests", () => {
  //-------------------Before All------------------------------------
  test.beforeAll(async ({ baseURL }) => {
    console.log("Starting test suite setup for Dashboard Page Tests");
    try {
      const browser: Browser = await chromium.launch({ headless: true });
      page = await browser.newPage();
      await page.goto(`${baseURL}`);
      // Initialize page objects
      console.log("Initializing page objects...");
      loginPage = new LoginPage(page);
      dashboardPage = new DashboardPage(page);
      demandPlanPage = new DemandPlanPage(page);
      tasksPage = new TaskPage(page);
      usersPage = new UsersPage(page);
      rolesPage = new RolesPage(page);
      groupsPage = new GroupsPage(page);
      hypercubePage = new HypercubePage(page);
      defaltDashboardCustomization = new DefaltDashboardCustomization(page);
      dashboardCustomizationPage = new DashboardCustomizationPage(page);
      //Initialize page elements
      await loginPage.initializeElements();
      await dashboardPage.initializeElements();
      await demandPlanPage.initializeElements();
      await tasksPage.initializeElements();
      await usersPage.initializeElements();
      await rolesPage.pageElements();
      await groupsPage.initializeElements();
      await defaltDashboardCustomization.pageElements();
      // Perform login before all tests
      console.log("Performing login...");
      await loginPage.BeforeEachloginProcess();
      console.log("Login completed");
      console.log('Dashboard test suite setup completed');
    } catch (error) {
      console.error("Error in Dashboard Page beforeAll setup:", error);
    }
  });
  //-------------------Before Each------------------------------------
  test.beforeEach(async ({ }, testInfo) => {
    console.log(`========== STARTING TEST: ${testInfo.title} ==========`);
    // Reset all test variables to initial state
    expectedTitle = "";
    actualTitle = "";
    // Navigate to Dashboard page and ensure it's fully loaded
    await dashboardPage.clickDashboardPageLink();
    console.log("Individual test setup completed");
    await expect(dashboardPage.dashboardPageLink).toBeVisible({ timeout: 15000 });
    // Set up test-specific prerequisites based on test title
    console.log(`Setting up prerequisites for test: ${testInfo.title}`);
  });
  //-----------------Test Cases--------------------------------------
  test("should have correct page title", async () => {
    expectedTitle = "Demand Intelligence";
    actualTitle = await dashboardPage.getPageTitle(expectedTitle);
    await expect(actualTitle).toBe(expectedTitle);
  });
  test("should have correct page URL", async () => {
    await expect(page).toHaveURL(/.*home/);
  });
  test("should have all required UI elements visible", async () => {
    await expect(dashboardPage.logoutButton).toBeVisible();
    await expect(dashboardPage.hypercubeButton).toBeVisible();
    await expect(dashboardPage.darkLightModeButton).toBeVisible();
    await expect(dashboardPage.notificationButton).toBeVisible();
    await expect(dashboardPage.userProfileButton).toBeVisible();
    await expect(dashboardPage.dashboardPageLink).toBeVisible();
    await expect(dashboardPage.demandPlanPageLink).toBeVisible();
    await expect(dashboardPage.tasksPageLink).toBeVisible();
    await expect(dashboardPage.usersPageLink).toBeVisible();
    await expect(dashboardPage.rolesPageLink).toBeVisible();
    await expect(dashboardPage.groupsPageLink).toBeVisible();
    await expect(dashboardPage.hypercubePageLink).toBeVisible();
    await expect(dashboardPage.editDefaultPageLink).toBeVisible();
    await expect(dashboardPage.customizeDashboardPageLink).toBeVisible();
    await expect(dashboardPage.productsDropdown).toBeVisible();
    await expect(dashboardPage.locationsDropdown).toBeVisible();
    await expect(dashboardPage.accuracyDropdown1).toBeVisible();
    await expect(dashboardPage.accuracyDropdown2).toBeVisible();
    await expect(dashboardPage.accuracyDropdown3).toBeVisible();
    await expect(dashboardPage.openTab).toBeVisible();
  });
  test("should have all required UI elements enabled", async () => {
    await expect(dashboardPage.logoutButton).toBeEnabled();
    await expect(dashboardPage.hypercubeButton).toBeEnabled();
    await expect(dashboardPage.darkLightModeButton).toBeEnabled();
    await expect(dashboardPage.notificationButton).toBeEnabled();
    await expect(dashboardPage.userProfileButton).toBeEnabled();
    await expect(dashboardPage.dashboardPageLink).toBeEnabled();
    await expect(dashboardPage.demandPlanPageLink).toBeEnabled();
    await expect(dashboardPage.tasksPageLink).toBeEnabled();
    await expect(dashboardPage.usersPageLink).toBeEnabled();
    await expect(dashboardPage.rolesPageLink).toBeEnabled();
    await expect(dashboardPage.groupsPageLink).toBeEnabled();
    await expect(dashboardPage.hypercubePageLink).toBeEnabled();
    await expect(dashboardPage.editDefaultPageLink).toBeEnabled();
    await expect(dashboardPage.customizeDashboardPageLink).toBeEnabled();
    await expect(dashboardPage.productsDropdown).toBeEnabled();
    await expect(dashboardPage.locationsDropdown).toBeEnabled();
    await expect(dashboardPage.accuracyDropdown1).toBeEnabled();
    await expect(dashboardPage.accuracyDropdown2).toBeEnabled();
    await expect(dashboardPage.accuracyDropdown3).toBeEnabled();
   await expect(dashboardPage.openTab).toBeEnabled();
  });
  test("should verify all navigation links are visible", async () => {
    const allLinksVisible =
      await dashboardPage.checkAllNavigationLinksVisible();
    await expect(allLinksVisible).toBeTruthy();
  });
  test("should navigate to Dashboard page", async () => {
    await dashboardPage.clickDashboardPageLink();
    await expect(page).toHaveURL(/.*home/);
    await expect(dashboardPage.logoutButton).toBeVisible();
   await expect(dashboardPage.openTab).toBeVisible();
    await expect(dashboardPage.productsDropdown).toBeVisible();
    await expect(dashboardPage.locationsDropdown).toBeVisible();
    await expect(dashboardPage.accuracyDropdown1).toBeVisible();
    await expect(dashboardPage.accuracyDropdown2).toBeVisible();
    await expect(dashboardPage.accuracyDropdown3).toBeVisible();
    await expect(dashboardPage.editDefaultPageLink).toBeVisible();
    await expect(dashboardPage.customizeDashboardPageLink).toBeVisible();
    await expect(dashboardPage.dashboardPageLink).toBeVisible();
    await expect(dashboardPage.demandPlanPageLink).toBeVisible();
    await expect(dashboardPage.tasksPageLink).toBeVisible();
    await expect(dashboardPage.usersPageLink).toBeVisible();
    await expect(dashboardPage.rolesPageLink).toBeVisible();
    await expect(dashboardPage.groupsPageLink).toBeVisible();
    await expect(dashboardPage.hypercubePageLink).toBeVisible();
    await expect(dashboardPage.logoutButton).toBeVisible();
    await expect(dashboardPage.hypercubeButton).toBeVisible();
    await expect(dashboardPage.darkLightModeButton).toBeVisible();
    await expect(dashboardPage.notificationButton).toBeVisible();
    await expect(dashboardPage.userProfileButton).toBeVisible();
  });
  test("should navigate to Demand Plan page", async () => {
    await dashboardPage.clickDemandPlanPageLink();
    await expect(page).toHaveURL(/.*demand-plan/);
    await expect(demandPlanPage.openDraftsDrawerButton).toBeVisible();
    await expect(demandPlanPage.demandPlanPageLabel).toBeVisible();
    await expect(demandPlanPage.openSavedViewsListButton).toBeVisible();
    await expect(demandPlanPage.openGraphSectionButton).toBeVisible();
    await expect(demandPlanPage.openAddViewFormButton).toBeVisible();
    await expect(demandPlanPage.openSavedViewsSettingFormButton).toBeVisible();
    await expect(demandPlanPage.expandLessButton).toBeVisible();
    await expect(demandPlanPage.exportToExcelExpandMore).toBeVisible();
    await expect(demandPlanPage.openAddTaskFormButton).toBeVisible();
    await expect(demandPlanPage.openAddDraftFormButtonFromDemandPlanPage).toBeVisible();
    await expect(demandPlanPage.fullScreenViewButton).toBeVisible();
    await expect(demandPlanPage.openCopyLinkUrlFormButton).toBeVisible();
    await expect(demandPlanPage.lockRowButton).toBeVisible();
    await expect(dashboardPage.logoutButton).toBeVisible();
    await expect(dashboardPage.hypercubeButton).toBeVisible();
    await expect(dashboardPage.darkLightModeButton).toBeVisible();
    await expect(dashboardPage.notificationButton).toBeVisible();
    await expect(dashboardPage.userProfileButton).toBeVisible();
    await expect(dashboardPage.dashboardPageLink).toBeVisible();
    await expect(dashboardPage.demandPlanPageLink).toBeVisible();
    await expect(dashboardPage.tasksPageLink).toBeVisible();
    await expect(dashboardPage.usersPageLink).toBeVisible();
    await expect(dashboardPage.rolesPageLink).toBeVisible();
    await expect(dashboardPage.groupsPageLink).toBeVisible();
    await expect(dashboardPage.hypercubePageLink).toBeVisible();
  });
  test("should navigate to Tasks page", async () => {
    await dashboardPage.clickTasksPageLink();
    await expect(page).toHaveURL(/.*tasks/);
    await expect(tasksPage.BtnCreateTask).toBeVisible();
    await expect(tasksPage.BtnAll).toBeVisible();
    await expect(tasksPage.BtnOverdue).toBeVisible();
    await expect(tasksPage.BtnCreated).toBeVisible();
    await expect(dashboardPage.logoutButton).toBeVisible();
    await expect(dashboardPage.hypercubeButton).toBeVisible();
    await expect(dashboardPage.darkLightModeButton).toBeVisible();
    await expect(dashboardPage.notificationButton).toBeVisible();
    await expect(dashboardPage.userProfileButton).toBeVisible();
    await expect(dashboardPage.dashboardPageLink).toBeVisible();
    await expect(dashboardPage.demandPlanPageLink).toBeVisible();
    await expect(dashboardPage.tasksPageLink).toBeVisible();
    await expect(dashboardPage.usersPageLink).toBeVisible();
    await expect(dashboardPage.rolesPageLink).toBeVisible();
    await expect(dashboardPage.groupsPageLink).toBeVisible();
    await expect(dashboardPage.hypercubePageLink).toBeVisible();
  });
  test("should navigate to Users page", async () => {
    await dashboardPage.clickUsersPageLink();
    await expect(page).toHaveURL(/.*users/);
    await expect(usersPage.BtnAddUser).toBeVisible();
    await expect(dashboardPage.logoutButton).toBeVisible();
    await expect(dashboardPage.hypercubeButton).toBeVisible();
    await expect(dashboardPage.darkLightModeButton).toBeVisible();
    await expect(dashboardPage.notificationButton).toBeVisible();
    await expect(dashboardPage.userProfileButton).toBeVisible();
    await expect(dashboardPage.dashboardPageLink).toBeVisible();
    await expect(dashboardPage.demandPlanPageLink).toBeVisible();
    await expect(dashboardPage.tasksPageLink).toBeVisible();
    await expect(dashboardPage.usersPageLink).toBeVisible();
    await expect(dashboardPage.rolesPageLink).toBeVisible();
    await expect(dashboardPage.groupsPageLink).toBeVisible();
    await expect(dashboardPage.hypercubePageLink).toBeVisible();
  });
  test("should navigate to Roles page", async () => {
    await dashboardPage.clickRolesPageLink();
    await expect(page).toHaveURL(/.*roles/);
    await expect(rolesPage.BtnAddRole).toBeVisible();
    await expect(dashboardPage.logoutButton).toBeVisible();
    await expect(dashboardPage.hypercubeButton).toBeVisible();
    await expect(dashboardPage.darkLightModeButton).toBeVisible();
    await expect(dashboardPage.notificationButton).toBeVisible();
    await expect(dashboardPage.userProfileButton).toBeVisible();
    await expect(dashboardPage.dashboardPageLink).toBeVisible();
    await expect(dashboardPage.demandPlanPageLink).toBeVisible();
    await expect(dashboardPage.tasksPageLink).toBeVisible();
    await expect(dashboardPage.usersPageLink).toBeVisible();
    await expect(dashboardPage.rolesPageLink).toBeVisible();
    await expect(dashboardPage.groupsPageLink).toBeVisible();
    await expect(dashboardPage.hypercubePageLink).toBeVisible();
  });
  test("should navigate to Groups page", async () => {
    await dashboardPage.clickGroupsPageLink();
    await expect(page).toHaveURL(/.*groups/);
    await expect(groupsPage.btnAddGroup).toBeVisible();
    await expect(dashboardPage.logoutButton).toBeVisible();
    await expect(dashboardPage.hypercubeButton).toBeVisible();
    await expect(dashboardPage.darkLightModeButton).toBeVisible();
    await expect(dashboardPage.notificationButton).toBeVisible();
    await expect(dashboardPage.userProfileButton).toBeVisible();
    await expect(dashboardPage.dashboardPageLink).toBeVisible();
    await expect(dashboardPage.demandPlanPageLink).toBeVisible();
    await expect(dashboardPage.tasksPageLink).toBeVisible();
    await expect(dashboardPage.usersPageLink).toBeVisible();
    await expect(dashboardPage.rolesPageLink).toBeVisible();
    await expect(dashboardPage.groupsPageLink).toBeVisible();
    await expect(dashboardPage.hypercubePageLink).toBeVisible();
    await expect(groupsPage.groupsPageLabel).toBeVisible();
  });
  test("should navigate to Hypercube page", async () => {
    await dashboardPage.clickHypercubePageLink();
    await expect(page).toHaveURL(/.*hypercube/);
    await expect(hypercubePage.BtnClear).toBeVisible();
  });
  test("should navigate to Edit Default Dashboard page", async () => {
    await dashboardPage.clickEditDefaultDashboardPageLink();
    console.log(await defaltDashboardCustomization.ChkboxTask);
    await expect(defaltDashboardCustomization.ChkboxTask).toBeVisible({
      timeout: 10000,
    });
  });
  test("should navigate to Customize Dashboard page", async () => {
    await dashboardPage.clickCustomizeDashboardPageLink();
    await expect(dashboardCustomizationPage.PageLabel).toBeVisible();
  });
  test("should toggle dark mode", async () => {
    await dashboardPage.toggleDarkMode();
    await page
      .locator("body")
      .evaluate((body) => body.classList.contains("dark-mode"));
  });
  test("should open notifications", async () => {
    await dashboardPage.clickNotification();
    await expect(page.locator(".title.text-center.mat-subtitle-1")).toBeVisible();
  });
  test("should interact with Products dropdown", async () => {
    await dashboardPage.clickProductsDropdown();
    await expect(dashboardPage.productsDropdownList).toBeVisible();
  });
  test("should interact with Locations dropdown", async () => {
    await dashboardPage.clickLocationsDropdown();
    await expect(dashboardPage.locationsDropdownList).toBeVisible();
  });
  test.skip("should switch between Past Due and Open tabs", async () => {
    // Switch to Past Due tab and verify
    await dashboardPage.clickPastDueTab();
    await expect(page.getByRole("tab", { name: "Past Due" })).toHaveAttribute(
      "aria-selected",
      "true"
    );
    await expect(
      page
        .getByRole("tab", { name: "Past Due" })
        .locator('xpath=//following::th[contains(text(), "Created By")]')
        .first()
    ).toBeVisible();
    // Switch to Open tab and verify
    await dashboardPage.clickOpenTab();
    await expect(page.getByRole("tab", { name: "Open" })).toHaveAttribute(
      "aria-selected",
      "true"
    );
    await expect(
      page
        .getByRole("tab", { name: "Open" })
        .locator('xpath=//following::th[contains(text(), "Created By")]')
        .first()
    ).toBeVisible();
  });
  test("should load dashboard within acceptable time", async () => {
    await dashboardPage.clickDashboardPageLink();
    const startTime = Date.now();
    await dashboardPage.logoutButton.waitFor({ state: "visible" });
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(5000);
    console.log(`Dashboard loaded in ${loadTime}ms`);
  });
  //--------------------After Each------------------------------------
  test.afterEach(async ({ }, testInfo) => {
    console.log(`========== FINISHING TEST: ${testInfo.title} ==========`);
    if (testInfo.status === 'passed') {
      console.log(`✅ TEST PASSED: ${testInfo.title}`);
    } else if (testInfo.status === 'failed') {
      console.log(`❌ TEST FAILED: ${testInfo.title}`);
    }
    // Clean up test artifacts based on test type
    console.log('Starting test cleanup...');
    await page.reload();
  });
  //-----------------------After All------------------------------------
  test.afterAll(async () => {
    console.log("Cleaning up after Dashboard Page tests...");
    try {
      // Navigate to Dashboard page to ensure we're in the right context
      await dashboardPage.clickDashboardPageLink();
      // Logout to leave the system in a clean state
      console.log("Logging out...");
      await dashboardPage.clickLogout();
      await expect(loginPage.userNameLocator).toBeVisible();
      console.log("Logged out successfully");
      // Close the browser
      console.log("Closing browser...");
      await page.close();
      console.log("Dashboard Page test cleanup completed");
    } catch (error) {
      console.error("Error in Dashboard Page afterAll cleanup:", error);
      // Ensure page is closed even if there's an error
      try {
        await page.close();
        console.log("Browser closed despite errors");
      } catch (closeError) {
        console.error("Failed to close browser:", closeError);
      }
    }
  });
});
