import test, { Browser, chromium, expect, Page } from '@playwright/test';
import GroupsPage from '../pages/groupsPage';
import DashboardPage from '../pages/dashboardPage';
import LoginPage from "../pages/loginPage";
const testData = JSON.parse(JSON.stringify(require("..//testData.json")));

let page: Page;
let groupsPage;
let dashboardPage;
let loginPage;

let ActualSuccessMessage: string;
let ExpectedSuccessMessage: string;
let ActualErrorMessage: string;
let ExpectedErrorMessage: string;
let ExpectedSuccessMessageDisplayedStatus: boolean;
let txtgroupname: string;
let txtgroupdescription: string;
let txtgrouplevel: string;
let expectedPageTitle: string;

test.describe('Groups Page Tests', () => {

    test.beforeAll(async ({ baseURL }) => {
        console.log("Starting test suite setup for Groups Page Tests");
        try {
            const browser: Browser = await chromium.launch({ headless: false });
            page = await browser.newPage();
            // Create a new page and navigate to the base URL
            console.log(`Navigating to base URL: ${baseURL}`);
            await page.goto(`${baseURL}`, { waitUntil: 'networkidle' });
            // Initialize page objects
            console.log('Initializing page objects...');
            dashboardPage = new DashboardPage(page);
            loginPage = new LoginPage(page);
            groupsPage = new GroupsPage(page);
            //Initialize page elements
            await loginPage.initializeElements();
            await dashboardPage.initializeElements();
            await groupsPage.initializeElements();
            //Perform login
            console.log('Performing login...');
            await loginPage.BeforeEachloginProcess();
            console.log('Login completed');
            await page.waitForLoadState('networkidle');
            //Verify successful login
            console.log('Verifying successful login...');
            await expect(dashboardPage.logoutButton).toBeVisible();
            console.log('Login verification completed');
            console.log('Demand Plan Page test suite setup completed');
        } catch (error) {
            console.error('Error in Demand Plan Page beforeAll setup:', error);
        }
    });

    test.beforeEach(async ({ }, testInfo) => {
        console.log(`========== STARTING TEST: ${testInfo.title} ==========`);
        // Reset all test variables to initial state
        expectedPageTitle = "";
        txtgroupname = "";
        txtgroupdescription = "";
        txtgrouplevel = "";
        ExpectedSuccessMessageDisplayedStatus = false;
        ExpectedSuccessMessage = "";
        ActualSuccessMessage = "";
        ExpectedErrorMessage = "";
        ActualErrorMessage = "";
        // Navigate to Groups page and ensure it's fully loaded
        console.log('Navigating to Groups page...');
        await dashboardPage.clickGroupsPageLink();
        await page.waitForLoadState('networkidle');
        await expect(groupsPage.groupsPageLabel).toBeVisible();
        // Set up test-specific prerequisites based on test title
        console.log(`Setting up prerequisites for test: ${testInfo.title}`);
        try {
            // groups-related test setup
            if (testInfo.title === 'should delete a group successfully'
                || testInfo.title === 'should cancel group deletion') {
                console.log(`Creating group for test: ${testInfo.title}`);
                txtgroupname = 'Test Group';
                txtgroupdescription = 'Test Group Description';
                ExpectedSuccessMessage = 'Group created successfully';
                await groupsPage.clickButtonAddGroup();
                ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
                ActualSuccessMessage = ActualSuccessMessage.trim();
                console.log('Actual Success Message: ' + ActualSuccessMessage);
                console.log('Expected Success Message: ' + ExpectedSuccessMessage);
                expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
                // Verify group appears in the list
                await groupsPage.searchForGroup(txtgroupname);
                const isPresent = await groupsPage.isGroupPresent(txtgroupname);
                console.log('Is group present: ' + isPresent);
                expect(isPresent).toBeTruthy();
            }
        } catch (setupError) {
            console.error(`Error during test setup for ${testInfo.title}:`, setupError);
            // Try to recover by reloading the page
            console.log('Attempting to recover from setup failure by reloading page...');
            await page.reload();
            await page.waitForLoadState('networkidle');
        }
        console.log(`Test setup completed for: ${testInfo.title}`);
    });


    test.afterEach(async () => {
        console.log("Test cleanup...");
        await page.reload();
    });

    test.describe.serial('Navigation and Basic UI Elements', () => {
        test('should have correct page label text', async () => {
            const pageLabelText = "Groups";
            await expect(groupsPage.groupsPageLabel).toHaveText(pageLabelText);
        });
        test('should have correct page title', async () => {
            const expectedTitle = "Demand Intelligence";
            const actualTitle = await dashboardPage.getPageTitle();
            await expect(actualTitle).toBe(expectedTitle);
        });
        test('should have correct page URL', async () => {
            await expect(page).toHaveURL(/.*groups/);
        });
        test('should have all required UI elements visible', async () => {
            await expect(groupsPage.groupsPageLabel).toBeVisible();
            await expect(groupsPage.groupListLabel).toBeVisible();
            await expect(groupsPage.columnHeaderName).toBeVisible();
            await expect(groupsPage.columnHeaderDescription).toBeVisible();
            await expect(groupsPage.columnHeaderStatus).toBeVisible();
            await expect(groupsPage.columnHeaderUsers).toBeVisible();
            await expect(groupsPage.columnHeaderCreatedBy).toBeVisible();
            await expect(groupsPage.columnHeaderDateCreated).toBeVisible();
            await expect(groupsPage.columnHeaderModifiedBy).toBeVisible();
            await expect(groupsPage.columnHeaderDateModified).toBeVisible();
            await expect(groupsPage.columnHeaderAction).toBeVisible();
            await expect(groupsPage.pageSizeDropdown).toBeVisible();
            await expect(groupsPage.firstPageButton).toBeVisible();
            await expect(groupsPage.previousPageButton).toBeVisible();
            await expect(groupsPage.nextPageButton).toBeVisible();
            await expect(groupsPage.lastPageButton).toBeVisible();
            await expect(groupsPage.btnAddGroup).toBeVisible();
            await expect(groupsPage.gridcellRowcopyButton).toBeVisible();
            await expect(groupsPage.gridcellRowdisableButton).toBeVisible();
            await expect(groupsPage.gridcellRowdeleteButton).toBeVisible();
            await expect(groupsPage.gridcellRoweditButton).toBeVisible();
        });
        test('should Navigate to Add Group Form Successfully', async () => {
            await groupsPage.clickButtonAddGroup();
            const pageLabelText = "Add Group";
            await expect(groupsPage.groupsPageLabel).toHaveText(pageLabelText);
            await expect(groupsPage.groupNameInputFieldlabel).toBeVisible();
            await expect(groupsPage.groupDescriptionInputFieldlabel).toBeVisible();
            await expect(groupsPage.groupAccessLabel).toBeVisible();
            await expect(groupsPage.selectLevelsLabel).toBeVisible();
            await expect(groupsPage.timeMeasurePermissionsLabel).toBeVisible();
            await expect(groupsPage.addGroupFormSaveButton).toBeVisible();
            await expect(groupsPage.groupNameInput).toBeVisible();
            await expect(groupsPage.groupDescriptionInput).toBeVisible();

        })
    });

    test.describe.serial('Group Creation', () => {
        test('should create a basic group successfully', async () => {
            txtgroupname = 'Test Group';
            txtgroupdescription = 'Test Group Description';
            ExpectedSuccessMessage = 'Group created successfully';

            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);

            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();
        });
        test.skip('should create a group with maximum length name', async () => {
            txtgroupname = 'Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test Group name Long Name Test';
            txtgroupdescription = 'Test Group Description';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup('Test Group name Long Name');
            const isPresent = await groupsPage.isGroupPresent('Test Group name Long Name');
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();
        });
        test('should create a group with maximum length description', async () => {
            txtgroupname = 'Test Group';
            txtgroupdescription = 'Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test Group Description Long Description Test';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();
        });
        test('should create a group with minimum length name', async () => {
            txtgroupname = 'T';
            txtgroupdescription = 'Test Group Description';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();
            // Delete the group
            await groupsPage.deleteGroup(txtgroupname);
        });
        test('should create a group with minimum length description', async () => {
            txtgroupname = 'Test Group';
            txtgroupdescription = 'T';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();
            // Delete the group
            await groupsPage.deleteGroup(txtgroupname);
        });
        test('should create a group with empty description', async () => {
            txtgroupname = 'Test Group';
            txtgroupdescription = '';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();
            // Delete the group
            await groupsPage.deleteGroup(txtgroupname);
        });

        test('should create a group with special characters', async () => {
            txtgroupname = 'Test Group Name with Special Characters !@#$%^&*()';
            txtgroupdescription = 'Test Group Description';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup('Test Group Name with Special Characters');
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();
            await groupsPage.deleteGroup('Test Group Name with Special Characters');
        });
        test('should not create a group with empty name', async () => {
            txtgroupname = '';
            txtgroupdescription = 'Test Group Description';
            ExpectedErrorMessage = 'Name is required';
            await groupsPage.clickButtonAddGroup();
            ActualErrorMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualErrorMessage = ActualErrorMessage.trim();
            console.log('Actual Error Message: ' + ActualErrorMessage);
            console.log('Expected Error Message: ' + ExpectedErrorMessage);
            expect(ActualErrorMessage).toBe(ExpectedErrorMessage);
        });
        test('should not create a group with duplicate name', async () => {
            // First create a group
            txtgroupname = 'Test Group First';
            txtgroupdescription = 'Test Group Description';
            ExpectedSuccessMessage = 'Group created successfully';
            ExpectedErrorMessage = 'Name is already in use';

            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();
            // Try to create another with the same name
            await groupsPage.clickButtonAddGroup();
            await groupsPage.fillGroupName(txtgroupname);
            await groupsPage.fillGroupDescription(txtgroupdescription);
            await groupsPage.clickButtonSaveAddGroupForm();
            ActualErrorMessage = await groupsPage.errorMessage.textContent() || '';
            ActualErrorMessage = ActualErrorMessage.trim();
            console.log('Actual Error Message: ' + ActualErrorMessage);
            console.log('Expected Error Message: ' + ExpectedErrorMessage);
            expect(ActualErrorMessage).toBe(ExpectedErrorMessage);
            await dashboardPage.clickGroupsPageLink();
        })
    });

    test.describe.serial('Group Deletion', () => {
        test('should delete a group successfully', async () => {
            txtgroupname = 'Test Group';
            ExpectedSuccessMessage = '';
            ExpectedSuccessMessage = 'Group deleted';
            ActualSuccessMessage = await groupsPage.deleteGroup(txtgroupname);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            await groupsPage.searchForGroup(txtgroupname);
            const isStillPresent = await groupsPage.isGroupPresent(txtgroupname);
            expect(isStillPresent).toBeFalsy();
        });
        test('should cancel group deletion', async () => {
            txtgroupname = 'Test Group';
            await groupsPage.searchForGroup(txtgroupname);
            await groupsPage.page.waitForTimeout(1000);
            await groupsPage.deleteGroupButtonClick();
            await groupsPage.cancelDeleteGroupButtonClick();
            // Verify group still exists
            await groupsPage.searchForGroup(txtgroupname);
            const isStillPresent = await groupsPage.isGroupPresent(txtgroupname);
            expect(isStillPresent).toBeTruthy();
            await groupsPage.deleteGroup(txtgroupname);
        });
    });

    test.describe.serial('Group Disable and Enable', () => {
        test('should disable a group successfully', async () => {
            // Create a group to disable
            txtgroupname = 'Test Group to test Disable Functionality';
            txtgroupdescription = 'Test Group Description to test Disable Functionality';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();

            ExpectedSuccessMessage = '';
            ExpectedSuccessMessage = 'Group disabled';
            ActualSuccessMessage = await groupsPage.disableGroup(txtgroupname);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            await groupsPage.deleteGroup(txtgroupname);
        });
        test('should enable a group successfully', async () => {
            // Create a group to enable
            txtgroupname = 'Test Group to test Enable Functionality';
            txtgroupdescription = 'Test Group Description to test Enable Functionality';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();

            ExpectedSuccessMessage = '';
            ExpectedSuccessMessage = 'Group enabled';
            ActualSuccessMessage = await groupsPage.disableGroup(txtgroupname);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            await groupsPage.deleteGroup(txtgroupname);
        });
    });
    test.describe.serial('Group Copy', () => {
        test('should copy a group successfully', async () => {
            // Create a group to copy
            txtgroupname = 'Test Group to test Copy Functionality';
            txtgroupdescription = 'Test Group Description to test Copy Functionality';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();

            // Copy the group
            ExpectedSuccessMessage = '';
            ExpectedSuccessMessage = 'Group created successfully';
            ActualSuccessMessage = await groupsPage.copyGroup(txtgroupname);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            await groupsPage.deleteGroup(txtgroupname);
            await groupsPage.deleteGroup(txtgroupname + ' Copy');
        });
    });
    test.describe.serial('Group Editing', () => {
        test('should edit a group successfully', async () => {
            // Create a group to edit
            txtgroupname = 'Test Group to test Edit Functionality';
            txtgroupdescription = 'Test Group Description to test Edit Functionality';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();

            // Edit the group
            ExpectedSuccessMessage = '';
            ExpectedSuccessMessage = 'Group updated successfully';
            ActualSuccessMessage = await groupsPage.editGroup(txtgroupname, 'New Description');
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            await groupsPage.deleteGroup(txtgroupname);
        });
    });
    test.describe.serial('Search Functionality', () => {
        test('should search for a group successfully', async () => {
            // Create a group to search for
            txtgroupname = 'Test Group to test Search Functionality';
            txtgroupdescription = 'Test Group Description to test Search Functionality';
            ExpectedSuccessMessage = 'Group created successfully';
            await groupsPage.clickButtonAddGroup();
            ActualSuccessMessage = await groupsPage.createGroup(txtgroupname, txtgroupdescription);
            ActualSuccessMessage = ActualSuccessMessage.trim();
            console.log('Actual Success Message: ' + ActualSuccessMessage);
            console.log('Expected Success Message: ' + ExpectedSuccessMessage);
            expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
            // Verify group appears in the list
            await groupsPage.searchForGroup(txtgroupname);
            const isPresent = await groupsPage.isGroupPresent(txtgroupname);
            console.log('Is group present: ' + isPresent);
            expect(isPresent).toBeTruthy();
            await groupsPage.deleteGroup(txtgroupname);
        });
    });
    test.describe.serial('Performance and Load Tests', () => {
        test('should load page within acceptable time', async () => {
            const loadTime = await groupsPage.measurePageLoadTime();
            expect(loadTime).toBeLessThan(5000);
            console.log(`Page loaded in ${loadTime}ms`);

        });
        test('should handle pagination correctly', async () => {
            // Assuming there's pagination on the page
            const paginationNext = page.locator('[data-testid="pagination-next"]');

            if (await paginationNext.isVisible()) {
                const initialFirstGroupName = await page.locator('[data-testid="group-row"]').first().textContent();

                // Go to next page
                await paginationNext.click();
                await page.waitForLoadState('networkidle');

                const secondPageFirstGroupName = await page.locator('[data-testid="group-row"]').first().textContent();

                expect(secondPageFirstGroupName).not.toBe(initialFirstGroupName);
            } else {
                console.log('Pagination not visible, skipping test');
                test.skip();
            }
        });
        test('should handle large data sets gracefully', async () => {
            // Assuming there are many groups
            const groupCount = await groupsPage.getGroupsCount();

            if (groupCount > 10) {
                // Check if pagination is working as expected
                const initialFirstGroupName = await page.locator('[data-testid="group-row"]').first().textContent();

                // Go to next page
                await page.locator('[data-testid="pagination-next"]').click();
                await page.waitForLoadState('networkidle');

                const secondPageFirstGroupName = await page.locator('[data-testid="group-row"]').first().textContent();

                expect(secondPageFirstGroupName).not.toBe(initialFirstGroupName);
            } else {
                console.log('Not enough groups to test pagination, skipping test');
                test.skip();
            }
        });
        test('should handle multiple users accessing the page', async () => {
            // This would require setting up multiple browser contexts
            // For simplicity, we'll skip this test
            test.skip();
        });

    });
    test.describe('Error Handling', () => {
        test('should handle server errors gracefully', async () => {
            await groupsPage.setupServerErrorMock();
            await groupsPage.clickButtonAddGroup();
            await groupsPage.createGroup('Test Group', 'Test Description');
            await expect(groupsPage.errorMessage).toBeVisible();
            await expect(groupsPage.errorMessage).toHaveText('Internal Server Error');
            await groupsPage.resetMocks();
        });

        test('should handle network timeouts gracefully', async () => {
            await groupsPage.setupNetworkTimeoutMock();
            await groupsPage.clickButtonAddGroup();
            await groupsPage.createGroup('Test Group', 'Test Description');
            await expect(groupsPage.errorMessage).toBeVisible();
            await expect(groupsPage.errorMessage).toHaveText('Request timed out');
            await groupsPage.resetMocks();
        });
    });

    test.describe('Accessibility Tests', () => {
        test('should have proper ARIA labels on form elements', async () => {
            await groupsPage.clickButtonAddGroup();
            await expect(groupsPage.groupNameInput).toHaveAttribute('aria-label', 'Group Name');
            await expect(groupsPage.groupDescriptionInput).toHaveAttribute('aria-label', 'Group Description');
            await expect(groupsPage.saveButton).toHaveAttribute('aria-label', 'Save Group');
        });
        test('should have proper tab navigation order', async () => {
            await groupsPage.clickButtonAddGroup();
            await groupsPage.groupNameInput.focus();
            await page.keyboard.press('Tab');
            await expect(groupsPage.groupDescriptionInput).toBeFocused();
            await page.keyboard.press('Tab');
            await expect(groupsPage.saveButton).toBeFocused();
        });
        test('should have no accessibility violations', async () => {
            const violations = await groupsPage.checkAccessibility();
            expect(violations).toHaveLength(0);
        });
    });
    //------------------After Each------------------------------------
    test.afterEach(async ({ }, testInfo) => {
        console.log(`========== FINISHING TEST: ${testInfo.title} ==========`);
        if (testInfo.status === 'passed') {
            console.log(`✅ TEST PASSED: ${testInfo.title}`);
        } else if (testInfo.status === 'failed') {
            console.log(`❌ TEST FAILED: ${testInfo.title}`);
        }
        // Clean up test artifacts based on test type
        console.log('Starting test cleanup...');
        try {
            // Clean up drafts if this was a group-related test
            if (testInfo.title === 'should create a basic group successfully'
                || testInfo.title === 'should create a group with maximum length name'
                || testInfo.title === 'should create a group with maximum length description'
                || testInfo.title === 'should create a group with minimum length name'
                || testInfo.title === 'should create a group with minimum length description'
                || testInfo.title === 'should create a group with empty description'
                || testInfo.title === 'should create a group with special characters'
                || testInfo.title === 'should not create a group with duplicate name'
                || testInfo.title === 'should not create a group with duplicate name'
                || testInfo.title === 'should not create a group with duplicate name'
            ) {
                console.log('Cleaning up test groups...');
                try {
                    // Check if the draft exists before trying to delete it
                    const groupExists = await page.isVisible(`text="${testData.txtgroupname}"`, { timeout: 3000 });
                    if (groupExists) {
                        await page.waitForTimeout(5000);
                        ExpectedSuccessMessage = '';
                        ActualSuccessMessage = '';
                        ExpectedSuccessMessage = 'Group Deleted';
                        console.log(`Deleting group: ${testData.txtgroupname}`);
                        // Delete the group
                        ActualSuccessMessage = await groupsPage.deleteGroup(txtgroupname);
                        ActualSuccessMessage = ActualSuccessMessage.trim();
                        console.log('Actual Success Message: ' + ActualSuccessMessage);
                        console.log('Expected Success Message: ' + ExpectedSuccessMessage);
                        expect(ActualSuccessMessage).toBe(ExpectedSuccessMessage);
                    }
                } catch (draftError) {
                    console.log(`Error cleaning up draft: ${draftError}`);
                }
            }

        } catch (cleanupError) {
            console.error(`Error during test cleanup: ${cleanupError}`);
        } finally {
            console.log('Reloading page to reset state...');
            try {
                await page.reload();
                await page.waitForLoadState('networkidle');
                console.log('Page reloaded successfully');
            } catch (reloadError) {
                console.error(`Error reloading page: ${reloadError}`);
            }

            console.log(`Test cleanup completed for: ${testInfo.title}`);
            console.log('='.repeat(60));
        }
    });
    //-------------------After All------------------------------------
    test.afterAll(async () => {
        console.log('Starting Groups Page test suite cleanup...');

        try {
            // Navigate to Groups page to ensure we're in the right context
            console.log('Navigating to Groups page for final cleanup...');
            await dashboardPage.clickGropusPageLink();
            await page.waitForLoadState('networkidle');

            // Final cleanup of test data
            console.log('Performing final cleanup of test data...');
            //await performFinalCleanup();

            // Logout to leave the system in a clean state
            console.log('Logging out...');
            await dashboardPage.clickLogout();
            await page.waitForLoadState('networkidle');
            await expect(loginPage.userNameLocator).toBeVisible({ timeout: 10000 });
            console.log('Logged out successfully');

            // Close the browser
            console.log('Closing browser...');
            await page.close();
            console.log('Groups Page test suite cleanup completed successfully');
        } catch (error) {
            console.error('Error during test suite cleanup:', error);
            // Ensure page is closed even if there's an error
            try {
                console.log('Attempting to close browser despite errors...');
                await page.close();
                console.log('Browser closed');
            } catch (closeError) {
                console.error('Failed to close browser:', closeError);
            }
        }
    });

});
