import { Locator, <PERSON> } from "@playwright/test";

export default class UsersPage {
    private page:Page;

        public BtnAddUser: Locator;
    

        constructor(page: Page) {
            this.page = page;
            this.initializeElements();
        }

        async initializeElements(){
            this.BtnAddUser = this.page.locator("//span[normalize-space()='Add User']");
         
       
        }
}