import { Locat<PERSON>, <PERSON> } from "@playwright/test";

export default class DashboardCustomizationPage {
    private page:Page;

        public PageLabel: Locator;
    

        constructor(page: Page) {
            this.page = page;
            this.pageElements();
        }

        async pageElements(){
            this.PageLabel = this.page.locator("//span[@class='title order-0 d-flex align-items-center']");
         
       
        }
}