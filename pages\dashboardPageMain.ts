import { Locator, <PERSON> } from "@playwright/test";

export default class DashboardPage {
  private page: Page;
  public logoutButton: Locator;
  public hypercubeButton: Locator;
  public darkLightModeButton: Locator;
  public notificationButton: Locator;
  public userProfileButton: Locator;
  public dashboardPageLink: Locator;
  public demandPlanPageLink: Locator;
  public tasksPageLink: Locator;
  public usersPageLink: Locator;
  public rolesPageLink: Locator;
  public groupsPageLink: Locator;
  public hypercubePageLink: Locator;
  public editDefaultPageLink: Locator;
  public customizeDashboardPageLink: Locator;
  public productsDropdown: Locator;
  public locationsDropdown: Locator;
  public accuracyDropdown1: Locator;
  public accuracyDropdown2: Locator;
  public accuracyDropdown3: Locator;
  public pastDuetab: Locator;
  public openTab: Locator;

  constructor(page: Page) {
    this.page = page;
  }

  async navigateToDashboard() {
    await this.page.goto("http://localhost:4123/#/home");
  }

  async pageElements() {
    this.logoutButton = this.page.locator(
      "//mat-icon[normalize-space()='power_settings_new']"
    );
    this.hypercubeButton = this.page.locator(
      "//mat-icon[@class='mat-icon notranslate material-icons-outlined mat-accent'][normalize-space()='emoji_objects']"
    );
    this.darkLightModeButton = this.page.locator(
      "//mat-icon[normalize-space()='dark_mode']"
    );
    this.notificationButton = this.page.locator(
      "//mat-icon[@class='mat-icon notranslate mat-badge material-icons-outlined mat-icon-no-color mat-badge-overlap mat-badge-above mat-badge-after mat-badge-medium ng-star-inserted']"
    );
    this.userProfileButton = this.page.locator(
      "/html[1]/body[1]/pag-root[1]/pag-layout[1]/mat-sidenav-container[1]/mat-sidenav-content[1]/mat-toolbar[1]/div[2]/div[2]/pag-user-button[1]/a[1]/span[2]"
    );

    this.dashboardPageLink = this.page.locator(
      "//mat-icon[normalize-space()='dashboard']"
    );
    this.demandPlanPageLink = this.page.locator(
      "//mat-icon[@class='mat-icon notranslate mat-mdc-tooltip-trigger material-icons-outlined mat-icon-no-color'][normalize-space()='insert_chart']"
    );

    this.tasksPageLink = this.page.locator(
      "//mat-icon[@class='mat-icon notranslate mat-mdc-tooltip-trigger material-icons-outlined mat-icon-no-color'][normalize-space()='fact_check']"
    );
    this.usersPageLink = this.page.locator(
      "//mat-icon[normalize-space()='account_circle']"
    );
    this.rolesPageLink = this.page.locator(
      "//mat-icon[normalize-space()='verified_user']"
    );
    this.groupsPageLink = this.page.locator(
      "//mat-icon[normalize-space()='people']"
    );
    this.hypercubePageLink = this.page.locator(
      "//mat-icon[@class='mat-icon notranslate mat-mdc-tooltip-trigger material-icons-outlined mat-icon-no-color'][normalize-space()='emoji_objects']"
    );
    this.editDefaultPageLink = this.page.locator(
      "//span[normalize-space()='Edit Default']"
    );
    this.customizeDashboardPageLink = this.page.locator(
      "//span[normalize-space()='Customize Dashboard']"
    );
    this.productsDropdown = this.page.locator(
      "//div[@class='mat-mdc-text-field-wrapper mdc-text-field ng-tns-c508571215-2 mdc-text-field--outlined mdc-text-field--no-label']"
    );
    this.locationsDropdown = this.page.locator(
      "//div[@class='mat-mdc-text-field-wrapper mdc-text-field ng-tns-c508571215-4 mdc-text-field--outlined mdc-text-field--no-label']"
    );
    this.accuracyDropdown1 = this.page.locator(
      "//div[@id='mat-select-value-1']"
    );
    this.accuracyDropdown2 = this.page.locator(
      "//div[@id='mat-select-value-3']"
    );
    this.accuracyDropdown3 = this.page.locator(
      "//div[@id='mat-select-value-5']"
    );
    this.pastDuetab = this.page.locator(
      "//span[contains(text(),'Past Due (7)')]"
    );
    this.openTab = this.page.locator(
      "//span[@class='mdc-tab__text-label'][normalize-space()='Open']"
    );
  }

  async isHypercubeIconVisible() {
    let isHypercubeVisible: boolean;
    await this.hypercubeButton.waitFor({ state: "visible" });
    isHypercubeVisible = await this.hypercubeButton.isVisible();
    return isHypercubeVisible;
  }
  async isDarkLightModeButtonVisible() {
    let isDarkLightModeVisible: boolean;
    await this.darkLightModeButton.waitFor({ state: "visible" });
    isDarkLightModeVisible = await this.darkLightModeButton.isVisible();
    return isDarkLightModeVisible;
  }
  async isNotoficationIconVisible() {
    let isNotoficationVisible: boolean;
    await this.notificationButton.waitFor({ state: "visible" });
    isNotoficationVisible = await this.notificationButton.isVisible();
    return isNotoficationVisible;
  }
  async isUserProfileIconVisible() {
    let isUserProfileVisible: boolean;
    await this.userProfileButton.waitFor({ state: "visible" });
    isUserProfileVisible = await this.userProfileButton.isVisible();
    return isUserProfileVisible;
  }

  async isNavigateToDashboardPageIconVisible() {
    let isNavigateToDashboardPageLinkIconVisible: boolean;
    await this.dashboardPageLink.waitFor({ state: "visible" });
    isNavigateToDashboardPageLinkIconVisible =
      await this.dashboardPageLink.isVisible();
    return isNavigateToDashboardPageLinkIconVisible;
  }
  async isNavigateToDemandPlanPageIconVisible() {
    let isNavigateToDemandPlanLinkIconVisible: boolean;
    await this.dashboardPageLink.waitFor({ state: "visible" });
    isNavigateToDemandPlanLinkIconVisible =
      await this.demandPlanPageLink.isVisible();
    return isNavigateToDemandPlanLinkIconVisible;
  }
  async isNavigateToTaskPageIconVisible() {
    let isNavigateToDashboardPageLinkIconVisible: boolean;
    await this.dashboardPageLink.waitFor({ state: "visible" });
    isNavigateToDashboardPageLinkIconVisible =
      await this.dashboardPageLink.isVisible();
    return isNavigateToDashboardPageLinkIconVisible;
  }
  async isNavigateToUserPageIconVisible() {
    let isNavigateToDashboardPageLinkIconVisible: boolean;
    await this.dashboardPageLink.waitFor({ state: "visible" });
    isNavigateToDashboardPageLinkIconVisible =
      await this.dashboardPageLink.isVisible();
    return isNavigateToDashboardPageLinkIconVisible;
  }
  async isNavigateToRolePageIconVisible() {
    let isNavigateToDashboardPageLinkIconVisible: boolean;
    await this.dashboardPageLink.waitFor({ state: "visible" });
    isNavigateToDashboardPageLinkIconVisible =
      await this.dashboardPageLink.isVisible();
    return isNavigateToDashboardPageLinkIconVisible;
  }
  async isNavigateToGroupPageIconVisible() {
    let isNavigateToDashboardPageLinkIconVisible: boolean;
    await this.dashboardPageLink.waitFor({ state: "visible" });
    isNavigateToDashboardPageLinkIconVisible =
      await this.dashboardPageLink.isVisible();
    return isNavigateToDashboardPageLinkIconVisible;
  }
  async isNavigateToHypercubePageIconVisible() {
    let isNavigateToDashboardPageLinkIconVisible: boolean;
    await this.dashboardPageLink.waitFor({ state: "visible" });
    isNavigateToDashboardPageLinkIconVisible =
      await this.dashboardPageLink.isVisible();
    return isNavigateToDashboardPageLinkIconVisible;
  }

  async clickButtonLogout() {
    console.log("Dashboard Page - Clicking Logout button");
    await this.logoutButton.click();
  }
  async clickButtonnotification() {
    console.log("Dashboard Page - Clicking Notification button");
    await this.notificationButton.click();
  }
  async clickButtonDarkLightMode() {
    console.log("Dashboard Page - Clicking Dark/Light Mode button");
    await this.darkLightModeButton.click();
  }
  async clickButtonHyperCube() {
    console.log("Dashboard Page - Clicking HyperCube button");
    await this.hypercubeButton.click();
  }

  async clickDashboardPageLink() {
    console.log("Dashboard Page - Clicking dashboard page link");
    await this.dashboardPageLink.click();
  }
  async clickDemandPlanPageLink() {
    console.log("Dashboard Page - Clicking demand plan page link");
    await this.demandPlanPageLink.click();
  }
  async clickTasksPageLink() {
    console.log("Dashboard Page - Clicking tasks page link");
    await this.tasksPageLink.click();
  }
  async clickUsersPageLink() {
    console.log("Dashboard Page - Clicking users page link");
    await this.usersPageLink.click();
  }
  async clickRolesPageLink() {
    console.log("Dashboard Page - Clicking roles page link");
    await this.rolesPageLink.click();
  }
  async clickGroupsPageLink() {
    console.log("Dashboard Page - Clicking groups page link");
    await this.groupsPageLink.click();
  }
  async clickHypercubePageLink() {
    console.log("Dashboard Page - Clicking hypercube page link");
    await this.hypercubePageLink.click();
  }

  async clickEditDefaultDashboardPageLink() {
    console.log("Dashboard Page - Clicking edit default page link");
    await this.editDefaultPageLink.click();
  }

  async clickCustomizeDashboardPageLink() {
    console.log("Dashboard Page - Clicking customize dashboard page link");
    await this.customizeDashboardPageLink.click();
  }

  async clickProductsDropdown() {
    console.log("Dashboard Page - Clicking on Products Dropdown");
    await this.productsDropdown.click();
  }
  async clickLocationDropdown() {
    console.log("Dashboard Page - Clicking on Location Dropdown");
    await this.locationsDropdown.click();
  }
  async clickAccuracyDropdown1Dropdown() {
    console.log("Dashboard Page - Clicking on Accuracy Dropdown 1");
    await this.accuracyDropdown1.click();
  }
  async clickAccuracyDropdown2Dropdown() {
    console.log("Dashboard Page - Clicking on Accuracy Dropdown 2");
    await this.accuracyDropdown2.click();
  }
  async clickAccuracyDropdown3Dropdown() {
    console.log("Dashboard Page - Clicking on Accuracy Dropdown 3");
    await this.accuracyDropdown3.click();
  }
  async clickPastDueTab() {
    console.log("Dashboard Page - Clicking on Past Due Tab");
    await this.pastDuetab.click();
  }
  async clickOpenTab() {
    console.log("Dashboard Page - Clicking on Open Tab");
    await this.openTab.click();
  }

  async getPageTitle(): Promise<string> {
    const title = await this.page.title();
    console.log("Actual Page Title - " + title);
    return title;
  }
}
