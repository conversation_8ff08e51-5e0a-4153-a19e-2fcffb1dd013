import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('http://localhost:8080/realms/demand-platform/protocol/openid-connect/auth?client_id=web-ui&redirect_uri=http%3A%2F%2Flocalhost%3A4123%2F&state=a7380890-4362-498f-82fe-def0ec9a7ba0&response_mode=fragment&response_type=code&scope=openid&nonce=a3a8fe0b-1932-44fe-aae3-7dbda65f7b54&code_challenge=2AEDpS3RSxKukvg94F2W20SJxtbWaYLvVDsLwAhYzHM&code_challenge_method=S256');
  await page.getByRole('textbox', { name: 'Username or email' }).fill('User');
  await page.getByRole('textbox', { name: 'Username or email' }).press('Tab');
  await page.getByRole('textbox', { name: 'Password' }).fill('Passeord');
  await page.getByRole('link').filter({ hasText: 'store' }).click();
  await page.getByRole('link').filter({ hasText: 'sell' }).click();
  await page.getByRole('link').filter({ hasText: 'insert_chart' }).click();
  await page.getByRole('link').filter({ hasText: 'fact_check' }).click();
  await page.getByRole('link').filter({ hasText: 'fact_check' }).click();
});